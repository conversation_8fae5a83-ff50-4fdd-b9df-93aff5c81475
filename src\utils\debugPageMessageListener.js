/**
 * Debug utilities for page message listener
 * Use these functions in the browser console to troubleshoot message receiving issues
 */

import { store } from '../redux/store';
import { getCollectionPaths } from '../services/firebase/collectionPaths';

/**
 * Get comprehensive debug information about the current page message listener setup
 */
export const debugPageMessageListener = () => {
    const state = store.getState().metaBusinessSuite;

    console.log('=== PAGE MESSAGE LISTENER DEBUG INFO ===');
    console.log('Current Redux State:', {
        selectedPage: state.selectedPage,
        selectedChat: state.selectedChat,
        selectedWhatsappChat: state.selectedWhatsappChat,
        messagesCount: state.messages?.length || 0,
        latestMessagesCount: state.latestMessages?.length || 0
    });

    // Check collection paths for current chat
    if (state.selectedChat && state.selectedPage?.id) {
        const chatType = state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger';
        const collectionPaths = getCollectionPaths(
            state.selectedChat.id,
            chatType,
            state.selectedPage.id,
            state.selectedChat
        );

        console.log('Expected Firebase Paths:', collectionPaths);
        console.log('Chat Type:', chatType);
        console.log('Sender ID:', state.selectedChat.participants?.data);
    }

    // Check WhatsApp paths
    if (state.selectedWhatsappChat && state.selectedPhone) {
        const phoneNumber = state.selectedWhatsappChat.sender_phone_number?.toString().trim().replace(/^\+|\s+/g, "");
        console.log('WhatsApp Paths:', {
            messagesPath: `whatsApp/${phoneNumber}/messages`,
            phoneNumber,
            selectedPhone: state.selectedPhone.display_phone_number
        });
    }

    console.log('=== END DEBUG INFO ===');

    return {
        state,
        paths: state.selectedChat ? getCollectionPaths(
            state.selectedChat.id,
            state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger',
            state.selectedPage?.id,
            state.selectedChat
        ) : null
    };
};

/**
 * Test the page message listener by simulating a message
 */
export const testPageMessageListener = async (testMessage = null) => {
    const state = store.getState().metaBusinessSuite;

    if (!state.selectedPage?.id) {
        console.error('No page selected for testing');
        return;
    }

    const defaultTestMessage = {
        id: `test_${Date.now()}`,
        message: 'Test message from debug utility',
        sender: state.selectedChat?.id || 'test_sender',
        recipient: state.selectedPage.id,
        created_time: new Date().toISOString(),
        type: 'text'
    };

    const messageToTest = testMessage || defaultTestMessage;

    console.log('Testing page message listener with message:', messageToTest);

    try {
        // Import and use the incoming message handler
        const { handleIncomingMessages } = await import('./incomingMessageHandler');

        const result = await handleIncomingMessages(
            store.dispatch,
            [messageToTest],
            state.selectedChat
        );

        console.log('Test result:', result);
        return result;
    } catch (error) {
        console.error('Test failed:', error);
        return { success: false, error: error.message };
    }
};

/**
 * Monitor Firebase writes in real-time (for debugging Laravel backend writes)
 */
export const monitorFirebaseWrites = (pageId) => {
    if (!pageId) {
        const state = store.getState().metaBusinessSuite;
        pageId = state.selectedPage?.id;
    }

    if (!pageId) {
        console.error('No pageId provided for monitoring');
        return;
    }

    console.log(`Starting Firebase write monitor for page ${pageId}`);

    // This would require additional Firebase listeners to monitor writes
    // For now, we'll just log the setup
    console.log('Monitor setup complete. Check browser Network tab for Firebase requests.');
    console.log('Expected Firebase collection pattern:', `${pageId}/{senderId}/messages`);

    return {
        pageId,
        expectedPattern: `${pageId}/{senderId}/messages`,
        instructions: [
            '1. Open browser DevTools',
            '2. Go to Network tab',
            '3. Filter by "firestore" or "firebase"',
            '4. Send a test message from Instagram/Messenger',
            '5. Look for POST requests to Firebase with the expected pattern'
        ]
    };
};

/**
 * Check if the current chat setup is correct for receiving messages
 */
export const validateChatSetup = () => {
    const state = store.getState().metaBusinessSuite;
    const issues = [];
    const recommendations = [];

    // Check basic setup
    if (!state.selectedPage?.id) {
        issues.push('No page selected');
        recommendations.push('Select a page from the dropdown');
    }

    if (!state.selectedChat && !state.selectedWhatsappChat) {
        issues.push('No chat selected');
        recommendations.push('Select a chat from the sidebar');
    }

    // Check Messenger/Instagram setup
    if (state.selectedChat) {
        if (!state.selectedChat.participants?.data) {
            issues.push('Selected chat missing participants data');
            recommendations.push('This chat may not be properly formatted for message receiving');
        }

        const chatType = state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger';
        const participantIndex = chatType === 'instagram' ? 1 : 0;
        const participantId = state.selectedChat.participants?.data?.[participantIndex]?.id;

        if (!participantId) {
            issues.push(`Missing participant ID for ${chatType}`);
            recommendations.push('Check if the chat object has the correct participant structure');
        }
    }

    // Check WhatsApp setup
    if (state.selectedWhatsappChat) {
        if (!state.selectedWhatsappChat.sender_phone_number) {
            issues.push('WhatsApp chat missing sender_phone_number');
            recommendations.push('Ensure WhatsApp chat has proper phone number');
        }

        if (!state.selectedPhone) {
            issues.push('No WhatsApp phone selected');
            recommendations.push('Select a WhatsApp business phone');
        }
    }

    console.log('=== CHAT SETUP VALIDATION ===');
    console.log('Issues found:', issues);
    console.log('Recommendations:', recommendations);
    console.log('Current setup is', issues.length === 0 ? '✅ VALID' : '❌ INVALID');
    console.log('=== END VALIDATION ===');

    return {
        isValid: issues.length === 0,
        issues,
        recommendations,
        currentState: {
            hasPage: !!state.selectedPage?.id,
            hasChat: !!(state.selectedChat || state.selectedWhatsappChat),
            chatType: state.selectedChat ?
                (state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger') :
                (state.selectedWhatsappChat ? 'whatsapp' : 'none')
        }
    };
};

// Make functions available globally for browser console
if (typeof window !== 'undefined') {
    window.debugPageMessageListener = debugPageMessageListener;
    window.testPageMessageListener = testPageMessageListener;
    window.monitorFirebaseWrites = monitorFirebaseWrites;
    window.validateChatSetup = validateChatSetup;
}

export {
    debugPageMessageListener,
    testPageMessageListener,
    monitorFirebaseWrites,
    validateChatSetup
};
