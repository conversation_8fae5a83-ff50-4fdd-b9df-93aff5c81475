/**
 * Debug utilities for page message listener
 * Use these functions in the browser console to troubleshoot message receiving issues
 */

import store from '../redux/store';
import { getCollectionPaths } from '../services/firebase/collectionPaths';

/**
 * Get comprehensive debug information about the current page message listener setup
 */
const debugPageMessageListener = () => {
    const state = store.getState().metaBusinessSuite;

    console.log('=== PAGE MESSAGE LISTENER DEBUG INFO ===');
    console.log('Current Redux State:', {
        selectedPage: state.selectedPage,
        selectedChat: state.selectedChat,
        selectedWhatsappChat: state.selectedWhatsappChat,
        messagesCount: state.messages?.length || 0,
        latestMessagesCount: state.latestMessages?.length || 0
    });

    // Show the correct participant ID that should be used for Firebase
    if (state.selectedChat?.participants?.data) {
        const chatType = state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger';
        const participantIndex = chatType === 'instagram' ? 1 : 0;
        const participantId = state.selectedChat.participants.data[participantIndex]?.id;

        console.log('Firebase Message Path Info:', {
            chatType,
            participantIndex,
            participantId,
            expectedFirebasePath: `${state.selectedPage?.page_id}/${participantId}/messages`,
            note: 'This is the participant ID used for Firebase messages (NOT chat ID)'
        });
    }

    // Check collection paths for current chat
    if (state.selectedChat && state.selectedPage?.page_id) {
        const chatType = state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger';
        const collectionPaths = getCollectionPaths(
            state.selectedChat,
            chatType,
            state.selectedPage.page_id.toString()
        );

        console.log('Expected Firebase Paths:', collectionPaths);
        console.log('Chat Type:', chatType);
        console.log('Sender ID:', state.selectedChat.participants?.data);
    }

    // Check WhatsApp paths
    if (state.selectedWhatsappChat && state.selectedPhone) {
        const phoneNumber = state.selectedWhatsappChat.sender_phone_number?.toString().trim().replace(/^\+|\s+/g, "");
        console.log('WhatsApp Paths:', {
            messagesPath: `whatsApp/${phoneNumber}/messages`,
            phoneNumber,
            selectedPhone: state.selectedPhone.display_phone_number
        });
    }

    console.log('=== END DEBUG INFO ===');

    return {
        state,
        paths: state.selectedChat ? getCollectionPaths(
            state.selectedChat,
            state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger',
            state.selectedPage?.page_id?.toString()
        ) : null
    };
};

/**
 * Test the page message listener by simulating a message
 */
const testPageMessageListener = async (testMessage = null) => {
    const state = store.getState().metaBusinessSuite;

    if (!state.selectedPage?.page_id) {
        console.error('No page selected for testing');
        return;
    }

    const defaultTestMessage = {
        id: `test_${Date.now()}`,
        message: 'Test message from debug utility',
        sender: state.selectedChat?.id || 'test_sender',
        recipient: state.selectedPage.page_id,
        created_time: new Date().toISOString(),
        type: 'text'
    };

    const messageToTest = testMessage || defaultTestMessage;

    console.log('Testing page message listener with message:', messageToTest);

    try {
        // Import and use the incoming message handler
        const { handleIncomingMessages } = await import('./incomingMessageHandler');

        const result = await handleIncomingMessages(
            store.dispatch,
            [messageToTest],
            state.selectedChat
        );

        console.log('Test result:', result);
        return result;
    } catch (error) {
        console.error('Test failed:', error);
        return { success: false, error: error.message };
    }
};

/**
 * Monitor Firebase writes in real-time (for debugging Laravel backend writes)
 */
const monitorFirebaseWrites = (pageId) => {
    if (!pageId) {
        const state = store.getState().metaBusinessSuite;
        pageId = state.selectedPage?.page_id;
    }

    if (!pageId) {
        console.error('No pageId provided for monitoring');
        return;
    }

    console.log(`Starting Firebase write monitor for page ${pageId}`);

    // This would require additional Firebase listeners to monitor writes
    // For now, we'll just log the setup
    console.log('Monitor setup complete. Check browser Network tab for Firebase requests.');
    console.log('Expected Firebase collection pattern:', `${pageId}/{senderId}/messages`);

    return {
        pageId,
        expectedPattern: `${pageId}/{senderId}/messages`,
        instructions: [
            '1. Open browser DevTools',
            '2. Go to Network tab',
            '3. Filter by "firestore" or "firebase"',
            '4. Send a test message from Instagram/Messenger',
            '5. Look for POST requests to Firebase with the expected pattern'
        ]
    };
};

/**
 * Check if the current chat setup is correct for receiving messages
 */
const validateChatSetup = () => {
    const state = store.getState().metaBusinessSuite;
    const issues = [];
    const recommendations = [];

    // Check basic setup
    if (!state.selectedPage?.page_id) {
        issues.push('No page selected');
        recommendations.push('Select a page from the dropdown');
    }

    if (!state.selectedChat && !state.selectedWhatsappChat) {
        issues.push('No chat selected');
        recommendations.push('Select a chat from the sidebar');
    }

    // Check Messenger/Instagram setup
    if (state.selectedChat) {
        if (!state.selectedChat.participants?.data) {
            issues.push('Selected chat missing participants data');
            recommendations.push('This chat may not be properly formatted for message receiving');
        }

        const chatType = state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger';
        const participantIndex = chatType === 'instagram' ? 1 : 0;
        const participantId = state.selectedChat.participants?.data?.[participantIndex]?.id;

        if (!participantId) {
            issues.push(`Missing participant ID for ${chatType}`);
            recommendations.push('Check if the chat object has the correct participant structure');
        }
    }

    // Check WhatsApp setup
    if (state.selectedWhatsappChat) {
        if (!state.selectedWhatsappChat.sender_phone_number) {
            issues.push('WhatsApp chat missing sender_phone_number');
            recommendations.push('Ensure WhatsApp chat has proper phone number');
        }

        if (!state.selectedPhone) {
            issues.push('No WhatsApp phone selected');
            recommendations.push('Select a WhatsApp business phone');
        }
    }

    console.log('=== CHAT SETUP VALIDATION ===');
    console.log('Issues found:', issues);
    console.log('Recommendations:', recommendations);
    console.log('Current setup is', issues.length === 0 ? '✅ VALID' : '❌ INVALID');
    console.log('=== END VALIDATION ===');

    return {
        isValid: issues.length === 0,
        issues,
        recommendations,
        currentState: {
            hasPage: !!state.selectedPage?.page_id,
            hasChat: !!(state.selectedChat || state.selectedWhatsappChat),
            chatType: state.selectedChat ?
                (state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger') :
                (state.selectedWhatsappChat ? 'whatsapp' : 'none')
        }
    };
};

/**
 * Debug the current selectedChat object structure
 */
const debugSelectedChat = () => {
    const state = store.getState().metaBusinessSuite;

    console.log('=== SELECTED CHAT DEBUG ===');
    console.log('selectedChat:', state.selectedChat);
    console.log('selectedPage:', state.selectedPage);
    console.log('selectedWhatsappChat:', state.selectedWhatsappChat);

    if (state.selectedChat) {
        console.log('selectedChat keys:', Object.keys(state.selectedChat));
        console.log('selectedChat.id:', state.selectedChat.id);
        console.log('selectedChat.participants:', state.selectedChat.participants);
        console.log('selectedChat.flage:', state.selectedChat.flage);

        // Try to determine what the sender ID should be
        const chatType = state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger';
        console.log('Determined chatType:', chatType);

        if (state.selectedChat.participants?.data) {
            console.log('Participants data:', state.selectedChat.participants.data);
            const participantIndex = chatType === 'instagram' ? 1 : 0;
            const senderId = state.selectedChat.participants.data[participantIndex]?.id;
            console.log(`Expected senderId (index ${participantIndex}):`, senderId);
        } else {
            console.log('No participants data found - will use chat ID as fallback');
        }
    }

    if (state.selectedPage) {
        console.log('selectedPage.page_id:', state.selectedPage.page_id);
        console.log('Expected Firebase path pattern:', `${state.selectedPage.page_id}/{senderId}/messages`);
    }

    console.log('=== END SELECTED CHAT DEBUG ===');

    return {
        selectedChat: state.selectedChat,
        selectedPage: state.selectedPage,
        selectedWhatsappChat: state.selectedWhatsappChat
    };
};

/**
 * Test the complete receiving message flow
 */
const testReceivingMessageFlow = async () => {
    const state = store.getState().metaBusinessSuite;

    console.log('=== TESTING RECEIVING MESSAGE FLOW ===');

    // Step 1: Check if we have the required data
    if (!state.selectedPage?.page_id) {
        console.error('❌ No page selected');
        return { success: false, error: 'No page selected' };
    }

    if (!state.selectedChat) {
        console.error('❌ No chat selected');
        return { success: false, error: 'No chat selected' };
    }

    const pageId = state.selectedPage.page_id.toString();
    console.log('✅ Page ID:', pageId);

    // Step 2: Determine the participant ID
    let participantId = null;
    if (state.selectedChat.participants?.data) {
        const chatType = state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger';
        const participantIndex = chatType === 'instagram' ? 1 : 0;
        participantId = state.selectedChat.participants.data[participantIndex]?.id;
        console.log('✅ Participant ID from participants.data:', participantId);
    } else {
        // Fallback to chat ID if no participants data
        participantId = state.selectedChat.id;
        console.log('⚠️ Using chat ID as fallback participant ID:', participantId);
    }

    if (!participantId) {
        console.error('❌ Could not determine participant ID');
        return { success: false, error: 'Could not determine participant ID' };
    }

    // Step 3: Test the Firebase path
    const expectedPath = `${pageId}/${participantId}/messages`;
    console.log('✅ Expected Firebase path:', expectedPath);

    // Step 4: Set up a test listener
    try {
        const { collection, query, orderBy, onSnapshot, limit } = await import('firebase/firestore');
        const { db } = await import('../utils/firebase.config');

        console.log('🔄 Setting up test listener...');

        const messagesQuery = query(
            collection(db, pageId, participantId, 'messages'),
            orderBy('created_time', 'desc'),
            limit(5)
        );

        const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
            console.log(`🔔 [TEST_LISTENER] Received ${snapshot.docs.length} messages`);

            if (snapshot.docs.length > 0) {
                console.log('📨 Latest messages:');
                snapshot.docs.forEach((doc, index) => {
                    const message = { id: doc.id, ...doc.data() };
                    console.log(`  ${index + 1}. [${message.created_time}] ${message.message || message.text}`);
                });
            } else {
                console.log('📭 No messages found in this collection');
            }
        }, (error) => {
            console.error('❌ [TEST_LISTENER] Error:', error);
        });

        console.log('✅ Test listener started successfully');
        console.log('📝 Instructions:');
        console.log('  1. Send a message from Instagram/Messenger to this chat');
        console.log('  2. Watch the console for new messages');
        console.log('  3. Call testListenerUnsubscribe() to stop the test');

        // Make unsubscribe available globally
        window.testListenerUnsubscribe = unsubscribe;

        return {
            success: true,
            pageId,
            participantId,
            expectedPath,
            unsubscribe
        };

    } catch (error) {
        console.error('❌ Failed to set up test listener:', error);
        return { success: false, error: error.message };
    }
};

/**
 * Test listening to a specific sender's messages
 */
const testSenderListener = async (pageId, senderId) => {
    if (!pageId || !senderId) {
        const state = store.getState().metaBusinessSuite;
        pageId = pageId || state.selectedPage?.page_id;
        senderId = senderId || state.selectedChat?.id;
    }

    if (!pageId || !senderId) {
        console.error('Missing pageId or senderId for testing');
        return;
    }

    console.log(`Testing direct listener for ${pageId}/${senderId}/messages`);

    try {
        // Import Firebase functions
        const { collection, query, orderBy, onSnapshot, limit } = await import('firebase/firestore');
        const { db } = await import('../utils/firebase.config');

        const messagesQuery = query(
            collection(db, pageId, senderId, 'messages'),
            orderBy('created_time', 'desc'),
            limit(10)
        );

        const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
            console.log(`[TEST_LISTENER] Received ${snapshot.docs.length} messages for ${senderId}`);

            snapshot.docs.forEach(doc => {
                const message = { id: doc.id, ...doc.data() };
                console.log(`[TEST_LISTENER] Message:`, message);
            });
        }, (error) => {
            console.error(`[TEST_LISTENER] Error:`, error);
        });

        console.log('Test listener started. Send a message to see if it appears.');
        console.log('Call unsubscribe() to stop the test listener.');

        // Make unsubscribe available globally
        window.testListenerUnsubscribe = unsubscribe;

        return unsubscribe;
    } catch (error) {
        console.error('Failed to set up test listener:', error);
    }
};

/**
 * Debug the current selectedChat object structure
 */
const debugSelectedChatStructure = () => {
    const state = store.getState().metaBusinessSuite;

    console.log('=== SELECTED CHAT STRUCTURE DEBUG ===');

    if (state.selectedChat) {
        console.log('selectedChat object:', state.selectedChat);
        console.log('selectedChat keys:', Object.keys(state.selectedChat));
        console.log('selectedChat.id:', state.selectedChat.id);
        console.log('selectedChat.participants:', state.selectedChat.participants);
        console.log('selectedChat.flage:', state.selectedChat.flage);

        // Check if participants data exists and what it looks like
        if (state.selectedChat.participants) {
            console.log('participants keys:', Object.keys(state.selectedChat.participants));
            console.log('participants.data:', state.selectedChat.participants.data);

            if (state.selectedChat.participants.data) {
                console.log('participants.data length:', state.selectedChat.participants.data.length);
                state.selectedChat.participants.data.forEach((participant, index) => {
                    console.log(`participant[${index}]:`, participant);
                });
            }
        }

        // Try to determine what should be used for Firebase paths
        const chatType = state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger';
        console.log('Determined chatType:', chatType);

        // Test the collection path functions
        try {
            const { getCollectionPaths } = require('../services/firebase/collectionPaths');
            const paths = getCollectionPaths(state.selectedChat, chatType, state.selectedPage?.page_id?.toString());
            console.log('Generated paths:', paths);
        } catch (error) {
            console.error('Error generating paths:', error);
        }
    } else {
        console.log('No selectedChat found');
    }

    console.log('=== END SELECTED CHAT STRUCTURE DEBUG ===');

    return state.selectedChat;
};

/**
 * Test if latest message persistence is working
 */
const testLatestMessagePersistence = async () => {
    const state = store.getState().metaBusinessSuite;

    console.log('=== TESTING LATEST MESSAGE PERSISTENCE ===');

    if (!state.selectedPage?.page_id || !state.selectedChat) {
        console.error('❌ Need to select both page and chat first');
        return { success: false, error: 'Missing page or chat selection' };
    }

    const pageId = state.selectedPage.page_id.toString();
    let participantId = null;

    // Determine participant ID
    if (state.selectedChat.participants?.data) {
        const chatType = state.selectedChat.flage === 'instagram' ? 'instagram' : 'messenger';
        const participantIndex = chatType === 'instagram' ? 1 : 0;
        participantId = state.selectedChat.participants.data[participantIndex]?.id;
    } else {
        participantId = state.selectedChat.id;
    }

    if (!participantId) {
        console.error('❌ Could not determine participant ID');
        return { success: false, error: 'Could not determine participant ID' };
    }

    try {
        const { doc, getDoc } = await import('firebase/firestore');
        const { db } = await import('../utils/firebase.config');

        // Check if parent document exists
        const parentDocRef = doc(db, pageId, participantId);
        const parentDoc = await getDoc(parentDocRef);

        console.log('📄 Parent document path:', `${pageId}/${participantId}`);
        console.log('📄 Parent document exists:', parentDoc.exists());

        if (parentDoc.exists()) {
            const data = parentDoc.data();
            console.log('📄 Parent document data:', data);
            console.log('📄 Latest message:', data.latest_message);
            console.log('📄 Latest message time:', data.latest_message_time);
        } else {
            console.log('⚠️ Parent document does not exist - will be created when you send a message');
        }

        console.log('✅ Test completed. Send a message and reload the page to see if latest message persists.');

        return {
            success: true,
            pageId,
            participantId,
            parentDocExists: parentDoc.exists(),
            parentDocData: parentDoc.exists() ? parentDoc.data() : null
        };

    } catch (error) {
        console.error('❌ Error testing latest message persistence:', error);
        return { success: false, error: error.message };
    }
};

/**
 * Monitor latest messages updates to track what's overriding them
 */
const monitorLatestMessagesUpdates = () => {
    console.log('=== MONITORING LATEST MESSAGES UPDATES ===');
    console.log('Watch the console for [REDUX] updateLatestMessages calls');
    console.log('This will help identify what is overriding your latest messages');

    const state = store.getState().metaBusinessSuite;
    console.log('Current latest messages count:', state.latestMessages?.length || 0);

    if (state.latestMessages?.length > 0) {
        console.log('Current latest messages:');
        state.latestMessages.slice(0, 3).forEach((msg, index) => {
            console.log(`  ${index + 1}. [${msg.created_time}] ${msg.message || msg.text} (from: ${msg.sender || msg.senderId})`);
        });
    }

    console.log('Now send a message and watch for Redux updates...');
    console.log('=== END MONITORING SETUP ===');

    return {
        currentCount: state.latestMessages?.length || 0,
        latestMessages: state.latestMessages
    };
};

/**
 * Check what listeners are currently active that might override latest messages
 */
const checkActiveListeners = () => {
    console.log('=== CHECKING ACTIVE LISTENERS ===');

    const state = store.getState().metaBusinessSuite;

    console.log('Active listeners status:');
    console.log('- messagesSnapshotUnsubscribe:', !!state.messagesSnapshotUnsubscribe);
    console.log('- latestMessagesUnsubscribe:', !!state.latestMessagesUnsubscribe);
    console.log('- pollingInterval:', !!state.pollingInterval);

    // Check if page message listener is active
    console.log('- Page message listener should be active for page:', state.selectedPage?.page_id);

    console.log('=== END ACTIVE LISTENERS CHECK ===');

    return {
        hasMessageListener: !!state.messagesSnapshotUnsubscribe,
        hasLatestMessagesListener: !!state.latestMessagesUnsubscribe,
        hasPolling: !!state.pollingInterval,
        selectedPageId: state.selectedPage?.page_id
    };
};

// Make functions available globally for browser console
if (typeof window !== 'undefined') {
    window.debugPageMessageListener = debugPageMessageListener;
    window.testPageMessageListener = testPageMessageListener;
    window.monitorFirebaseWrites = monitorFirebaseWrites;
    window.validateChatSetup = validateChatSetup;
    window.testSenderListener = testSenderListener;
    window.debugSelectedChatStructure = debugSelectedChatStructure;
    window.testReceivingMessageFlow = testReceivingMessageFlow;
    window.testLatestMessagePersistence = testLatestMessagePersistence;
    window.monitorLatestMessagesUpdates = monitorLatestMessagesUpdates;
    window.checkActiveListeners = checkActiveListeners;
}

export {
    debugPageMessageListener,
    testPageMessageListener,
    monitorFirebaseWrites,
    validateChatSetup,
    testSenderListener,
    debugSelectedChatStructure,
    testReceivingMessageFlow,
    testLatestMessagePersistence
};
