import {
    collection,
    addDoc,
    query,
    orderBy,
    onSnapshot,
    getDocs,
    getDoc,
    doc,
    updateDoc,
    arrayUnion,
    serverTimestamp,
    setDoc
} from 'firebase/firestore';
import { db } from '../../utils/firebase.config';
import {
    validateCommentText,
    validateUserPermissions,
    validateChatContext,
    validateAuthor
} from '../../utils/commentValidation';
import {
    executeWithErrorHandling,
    CommentError,
    ERROR_TYPES
} from '../../utils/commentErrorHandling';
import { getCollectionPaths } from '../firebase/collectionPaths';
import {
    fetchOptimizedComments,
    listenToCommentsOptimized
} from '../firebase/queryOptimization';

/**
 * Get the appropriate collection path based on chat type and ID using centralized service
 * @param {string} chatId - The chat identifier (phone number for WhatsApp, backend chat ID for Messenger/Instagram)
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {string} pageId - The page identifier for organizing chats by page
 * @param {Object} selectedChat - The selected chat object (optional, for getting participant data)
 * @returns {string} - The Firestore collection path
 */
const getCommentsCollectionPath = (chatId, chatType, pageId, selectedChat = null) => {
    // For comments, we need to create a mock selectedChat object if not provided
    // since comments use chat ID, not participant ID
    const chatObject = selectedChat || { id: chatId };
    const paths = getCollectionPaths(chatObject, chatType, pageId);
    return paths.comments;
};

/**
 * Ensure the parent document exists for the comments collection
 * This is needed because Firestore requires parent documents to exist for subcollections
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {string} pageId - The page identifier for organizing chats by page
 * @returns {Promise<void>}
 */
const ensureParentDocumentExists = async (chatId, chatType, pageId) => {
    try {
        // Validate that pageId and chatId are strings
        if (typeof pageId !== 'string' || typeof chatId !== 'string') {
            console.error('ensureParentDocumentExists: pageId and chatId must be strings', { pageId, chatId });
            return;
        }

        // Create the parent document using combined ID structure: chats/{pageId}_{chatId}
        // This maintains a 2-segment path for documents (even number) which is valid for Firebase
        const combinedChatId = `${pageId}_${chatId}`;
        console.log('ensureParentDocumentExists - creating doc with combined ID:', { pageId, chatId, combinedChatId });

        // Create the chat document with combined ID
        const parentDocRef = doc(db, 'chats', combinedChatId);
        await setDoc(parentDocRef, {
            chatId,
            chatType,
            pageId,
            combinedChatId,
            createdAt: serverTimestamp(),
            lastActivity: serverTimestamp()
        }, { merge: true });
    } catch (error) {
        console.error('Error ensuring parent document exists:', error);
        // Don't throw here as this is not critical for the main operation
    }
};

/**
 * Add a new comment to Firebase with comprehensive validation and error handling
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {string} text - The comment text
 * @param {Object} author - The author information {id, name, email}
 * @param {Object} user - Current user object for permission validation
 * @param {string} customId - Custom ID for the comment (optional)
 * @param {string} pageId - The page identifier for organizing chats by page
 * @returns {Promise<string>} - The ID of the created comment
 */
export const addComment = async (chatId, chatType, text, author, user = null, customId = null, pageId = null) => {
    const context = {
        operation: 'add comment',
        chatId,
        chatType,
        pageId,
        authorId: author?.id
    };

    return executeWithErrorHandling(async () => {
        // Validate chat context
        const chatValidation = validateChatContext(chatId, chatType);
        if (!chatValidation.isValid) {
            throw new CommentError(
                `Invalid chat context: ${chatValidation.errors.join(', ')}`,
                ERROR_TYPES.VALIDATION,
                null,
                context
            );
        }

        // Validate comment text
        const textValidation = validateCommentText(text);
        if (!textValidation.isValid) {
            throw new CommentError(
                `Invalid comment text: ${textValidation.errors.join(', ')}`,
                ERROR_TYPES.VALIDATION,
                null,
                context
            );
        }

        // Validate author information
        const authorValidation = validateAuthor(author);
        if (!authorValidation.isValid) {
            throw new CommentError(
                `Invalid author information: ${authorValidation.errors.join(', ')}`,
                ERROR_TYPES.VALIDATION,
                null,
                context
            );
        }

        // Validate user permissions if user object is provided
        // Note: All authenticated users can write comments, so this is mainly for logging/auditing
        if (user) {
            const permissionValidation = validateUserPermissions(user, 'write');
            if (!permissionValidation.isValid) {
                console.warn('User permission validation failed, but allowing comment creation:', permissionValidation.reason);
                // Don't throw error - allow all authenticated users to write comments
            }
        }

        // Ensure parent document exists before creating the comment
        await ensureParentDocumentExists(chatId, chatType, pageId);

        const collectionPath = getCommentsCollectionPath(chatId, chatType, pageId);

        if (!collectionPath) {
            throw new CommentError(
                'Unable to determine comments collection path',
                ERROR_TYPES.VALIDATION,
                null,
                context
            );
        }

        // Create collection reference using the path string split into segments
        const commentsRef = collection(db, ...collectionPath.split('/'));

        const commentData = {
            text: textValidation.sanitizedText,
            author: authorValidation.sanitizedAuthor,
            createdAt: serverTimestamp(),
            readBy: [],
            chatType,
            pageId
        };

        // If a custom ID is provided (for backend synchronization), use setDoc
        // Otherwise use addDoc to generate a random ID
        if (customId) {
            const docRef = doc(commentsRef, customId);
            await setDoc(docRef, commentData);
            return customId;
        } else {
            const docRef = await addDoc(commentsRef, commentData);
            return docRef.id;
        }
    }, context, {
        enableRetry: true,
        enableOfflineQueue: true,
        retryOptions: { maxRetries: 2 }
    });
};

/**
 * Fetch comments for a specific chat with validation and error handling
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {Object} user - Current user object for permission validation
 * @param {string} pageId - The page identifier for organizing chats by page
 * @returns {Promise<Array>} - Array of comments ordered by creation time
 */
export const fetchComments = async (chatId, chatType, user = null, pageId = null) => {
    const context = {
        operation: 'fetch comments',
        chatId,
        chatType,
        pageId
    };

    return executeWithErrorHandling(async () => {
        // Validate chat context
        const chatValidation = validateChatContext(chatId, chatType);
        if (!chatValidation.isValid) {
            throw new CommentError(
                `Invalid chat context: ${chatValidation.errors.join(', ')}`,
                ERROR_TYPES.VALIDATION,
                null,
                context
            );
        }

        // Validate user permissions if user object is provided
        // Note: All authenticated users can read comments
        if (user) {
            const permissionValidation = validateUserPermissions(user, 'read');
            if (!permissionValidation.isValid) {
                console.warn('User permission validation failed, but allowing comment reading:', permissionValidation.reason);
                // Don't throw error - allow all authenticated users to read comments
            }
        }

        // Ensure parent document exists before fetching comments
        await ensureParentDocumentExists(chatId, chatType, pageId);

        // Use optimized query with performance monitoring and caching
        const comments = await fetchOptimizedComments(chatId, chatType, {
            limit: 100,
            orderDirection: 'asc',
            useCache: true
        }, pageId);

        // Transform the data to match expected format
        return comments.map(comment => ({
            ...comment,
            createdAt: comment.createdAt?.toDate?.()?.toISOString() ||
                comment.createdAt ||
                new Date().toISOString()
        }));
    }, context, {
        enableRetry: true,
        enableOfflineQueue: false, // Don't queue read operations
        retryOptions: { maxRetries: 2 },
        errorOptions: {
            showToast: false // Don't show toast for fetch errors, handle in UI
        }
    }).catch(error => {
        // Return empty array for fetch errors to handle gracefully
        console.warn('Failed to fetch comments, returning empty array:', error.message);
        return [];
    });
};

/**
 * Listen to real-time comment updates using onSnapshot with validation and error handling
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {Function} callback - Callback function to handle comment updates
 * @param {Object} user - Current user object for permission validation
 * @param {string} pageId - The page identifier for organizing chats by page
 * @returns {Function} - Unsubscribe function to stop listening
 */
export const listenToComments = (chatId, chatType, callback, user = null, pageId = null) => {
    const context = {
        operation: 'listen to comments',
        chatId,
        chatType,
        pageId
    };

    let unsubscribeFunction = () => { };

    try {
        // Validate chat context
        const chatValidation = validateChatContext(chatId, chatType);
        if (!chatValidation.isValid) {
            const error = new CommentError(
                `Invalid chat context: ${chatValidation.errors.join(', ')}`,
                ERROR_TYPES.VALIDATION,
                null,
                context
            );
            callback([], error);
            return () => { };
        }

        // Validate user permissions if user object is provided
        // Note: All authenticated users can read comments
        if (user) {
            const permissionValidation = validateUserPermissions(user, 'read');
            if (!permissionValidation.isValid) {
                console.warn('User permission validation failed, but allowing comment listening:', permissionValidation.reason);
                // Don't throw error - allow all authenticated users to listen to comments
            }
        }

        // Ensure parent document exists before setting up listener
        ensureParentDocumentExists(chatId, chatType, pageId).then(() => {
            // Use optimized listener with performance monitoring
            unsubscribeFunction = listenToCommentsOptimized(chatId, chatType, (comments, error) => {
                if (error) {
                    const commentError = new CommentError(
                        'Error in optimized comments listener',
                        ERROR_TYPES.FIREBASE,
                        error,
                        context
                    );
                    callback([], commentError);
                    return;
                }

                try {
                    // Transform the data to match expected format
                    const transformedComments = comments.map(comment => ({
                        ...comment,
                        createdAt: comment.createdAt?.toDate?.()?.toISOString() ||
                            comment.createdAt ||
                            new Date().toISOString()
                    }));

                    callback(transformedComments);
                } catch (transformError) {
                    const commentError = new CommentError(
                        'Error processing optimized comment updates',
                        ERROR_TYPES.FIREBASE,
                        transformError,
                        context
                    );
                    callback([], commentError);
                }
            }, {
                limit: 100,
                orderDirection: 'asc'
            }, pageId);
        }).catch((error) => {
            const commentError = new CommentError(
                'Error setting up optimized comments listener',
                ERROR_TYPES.FIREBASE,
                error,
                context
            );
            callback([], commentError);
        });

        // Return the unsubscribe function (will be updated when the listener is set up)
        return () => {
            try {
                unsubscribeFunction();
            } catch (error) {
                console.warn('Error unsubscribing from comments listener:', error);
            }
        };
    } catch (error) {
        const commentError = new CommentError(
            'Error setting up comments listener',
            ERROR_TYPES.FIREBASE,
            error,
            context
        );
        callback([], commentError);
        return () => { };
    }
};

/**
 * Mark comments as read by a specific user with validation and error handling
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {Array<string>} commentIds - Array of comment IDs to mark as read
 * @param {Object} user - User information {id, name}
 * @param {Object} currentUser - Current user object for permission validation
 * @param {string} pageId - The page identifier for organizing chats by page
 * @returns {Promise<void>}
 */
export const markCommentsAsRead = async (chatId, chatType, commentIds, user, currentUser = null, pageId = null) => {
    const context = {
        operation: 'mark comments as read',
        chatId,
        chatType,
        pageId,
        commentIds: commentIds?.length || 0,
        userId: user?.id
    };

    return executeWithErrorHandling(async () => {
        // Validate chat context
        const chatValidation = validateChatContext(chatId, chatType);
        if (!chatValidation.isValid) {
            throw new CommentError(
                `Invalid chat context: ${chatValidation.errors.join(', ')}`,
                ERROR_TYPES.VALIDATION,
                null,
                context
            );
        }

        // Validate user information
        if (!user || !user.id || !user.name) {
            throw new CommentError(
                'Valid user information is required',
                ERROR_TYPES.VALIDATION,
                null,
                context
            );
        }

        // Validate comment IDs
        if (!Array.isArray(commentIds) || commentIds.length === 0) {
            throw new CommentError(
                'Valid comment IDs are required',
                ERROR_TYPES.VALIDATION,
                null,
                context
            );
        }

        // Validate user permissions if currentUser object is provided
        // Note: All authenticated users can read comments (needed for marking as read)
        if (currentUser) {
            const permissionValidation = validateUserPermissions(currentUser, 'read');
            if (!permissionValidation.isValid) {
                console.warn('User permission validation failed, but allowing mark as read:', permissionValidation.reason);
                // Don't throw error - allow all authenticated users to mark comments as read
            }
        }

        const collectionPath = getCommentsCollectionPath(chatId, chatType, pageId);
        const readReceipt = {
            userId: user.id,
            userName: user.name,
            userPhoto: user.photo || null,
            readAt: new Date().toISOString() // Use regular timestamp instead of serverTimestamp()
        };

        // Update each comment with the read receipt (with duplicate prevention)
        const updatePromises = commentIds.map(async (commentId) => {
            if (!commentId || typeof commentId !== 'string') {
                console.warn('Skipping invalid comment ID:', commentId);
                return Promise.resolve();
            }

            const commentRef = doc(db, ...collectionPath.split('/'), commentId);

            // Get current comment to check for existing read receipts
            const commentDoc = await getDoc(commentRef);
            if (commentDoc.exists()) {
                const currentData = commentDoc.data();
                const existingReadBy = currentData.readBy || [];

                // Check if user already has a read receipt
                const userAlreadyRead = existingReadBy.some(receipt =>
                    receipt.userId === user.id
                );

                if (!userAlreadyRead) {
                    // Only add read receipt if user hasn't read it yet
                    return updateDoc(commentRef, {
                        readBy: arrayUnion(readReceipt)
                    });
                } else {
                    console.log(`User ${user.id} already marked comment ${commentId} as read`);
                    return Promise.resolve();
                }
            } else {
                // Comment doesn't exist, skip
                console.warn(`Comment ${commentId} not found in Firebase`);
                return Promise.resolve();
            }
        });

        await Promise.all(updatePromises);
    }, context, {
        enableRetry: true,
        enableOfflineQueue: true,
        retryOptions: { maxRetries: 2 }
    });
};

/**
 * Initialize comments collection for a chat if it doesn't exist
 * This is useful for ensuring the collection structure is ready
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {string} pageId - The page identifier for organizing chats by page
 * @returns {Promise<boolean>} - Returns true if initialization was successful
 */
export const initializeCommentsCollection = async (chatId, chatType, pageId) => {
    try {
        await ensureParentDocumentExists(chatId, chatType, pageId);

        // Check if any comments exist
        const comments = await fetchComments(chatId, chatType, null, pageId);

        // Collection is now ready (either existed or was created)
        console.log(`Comments collection initialized for ${chatType} chat ${chatId}. Found ${comments.length} existing comments.`);
        return true;
    } catch (error) {
        console.error('Error initializing comments collection:', error);
        return false;
    }
};
