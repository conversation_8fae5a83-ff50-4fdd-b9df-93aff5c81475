/**
 * Custom hook for managing page-level message listeners
 * This hook automatically sets up listeners for all messages under a page
 * and updates Redux state when new messages are received from the Laravel backend
 */

import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { startPageMessageListener } from '../redux/features/metaBusinessChatSlice';

/**
 * Hook to manage page-level message listening
 * @param {string} pageId - The page ID to listen for messages
 * @param {Object} options - Configuration options
 * @returns {Object} - Status and control functions
 */
export const usePageMessageListener = (pageId, options = {}) => {
    const dispatch = useDispatch();
    const unsubscribeRef = useRef(null);
    const isListeningRef = useRef(false);

    // Get current state for debugging
    const selectedPage = useSelector(state => state.metaBusinessSuite.selectedPage);
    const selectedChat = useSelector(state => state.metaBusinessSuite.selectedChat);
    const messages = useSelector(state => state.metaBusinessSuite.messages);
    const latestMessages = useSelector(state => state.metaBusinessSuite.latestMessages);

    const {
        autoStart = true,
        enableLogging = true,
        onMessageReceived = null,
        onLatestMessageUpdate = null
    } = options;

    /**
     * Start the page message listener
     */
    const startListener = async () => {
        if (!pageId) {
            console.warn('[PAGE_MESSAGE_LISTENER] No pageId provided');
            return;
        }

        if (isListeningRef.current) {
            console.log(`[PAGE_MESSAGE_LISTENER] Already listening to page ${pageId}`);
            return;
        }

        console.log(`[PAGE_MESSAGE_LISTENER] Starting listener for page ${pageId}`);

        try {
            if (enableLogging) {
                console.log(`[PAGE_MESSAGE_LISTENER] Starting listener for page ${pageId}`);
            }

            const result = await dispatch(startPageMessageListener({ pageId }));

            if (result.type.endsWith('/fulfilled')) {
                unsubscribeRef.current = result.payload.unsubscribe;
                isListeningRef.current = true;

                if (enableLogging) {
                    console.log(`[PAGE_MESSAGE_LISTENER] Successfully started listener for page ${pageId}`);
                }
            } else {
                console.error(`[PAGE_MESSAGE_LISTENER] Failed to start listener:`, result.payload);
            }
        } catch (error) {
            console.error(`[PAGE_MESSAGE_LISTENER] Error starting listener:`, error);
        }
    };

    /**
     * Stop the page message listener
     */
    const stopListener = () => {
        if (unsubscribeRef.current) {
            if (enableLogging) {
                console.log(`[PAGE_MESSAGE_LISTENER] Stopping listener for page ${pageId}`);
            }

            unsubscribeRef.current();
            unsubscribeRef.current = null;
            isListeningRef.current = false;
        }
    };

    /**
     * Restart the listener (useful for debugging)
     */
    const restartListener = async () => {
        stopListener();
        await startListener();
    };

    // Auto-start listener when pageId changes
    useEffect(() => {
        if (autoStart && pageId) {
            startListener();
        }

        // Cleanup on unmount or pageId change
        return () => {
            stopListener();
        };
    }, [pageId, autoStart]);

    // Debug information
    const getDebugInfo = () => {
        return {
            pageId,
            isListening: isListeningRef.current,
            hasUnsubscribe: !!unsubscribeRef.current,
            selectedPage: selectedPage?.page_id,
            selectedChat: selectedChat?.id,
            messagesCount: messages?.length || 0,
            latestMessagesCount: latestMessages?.length || 0,
            timestamp: new Date().toISOString()
        };
    };

    return {
        isListening: isListeningRef.current,
        startListener,
        stopListener,
        restartListener,
        getDebugInfo
    };
};

/**
 * Simplified hook that just starts the listener automatically
 * @param {string} pageId - The page ID to listen for messages
 */
export const useAutoPageMessageListener = (pageId) => {
    return usePageMessageListener(pageId, {
        autoStart: true,
        enableLogging: true
    });
};

export default usePageMessageListener;
