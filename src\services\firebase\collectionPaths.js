/**
 * Firebase Collection Path Resolution Service
 *
 * This service provides centralized logic for determining consistent Firebase collection paths
 * for both messages and comments across different chat types (WhatsApp, Messenger, Instagram).
 *
 * The goal is to standardize collection paths so that both messages and comments use the same
 * document identifier pattern for consistent data access.
 */

/**
 * Extract the correct chat identifier based on chat type and selected cject
 * @ct} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @returns {string|null} - The chat identifier to use for Firebase collections
 */
export const getChatIdentifier = (selectedChat, chatType) => {
    if (!selectedChat) {
        console.warn('getChatIdentifier: selectedChat is null or undefined');
        return null;
    }

    if (chatType === 'whatsapp') {
        // For WhatsApp, use the normalized phone number
        const phoneNumber = selectedChat.sender_phone_number;
        if (!phoneNumber) {
            console.warn('getChatIdentifier: WhatsApp chat missing sender_phone_number');
            return null;
        }

        // Normalize phone number by removing + prefix and spaces
        return phoneNumber.toString().trim().replace(/^\+|\s+/g, "");
    }

    // For Messenger/Instagram, use the backend chat ID for consistency
    if (selectedChat.id) {
        return selectedChat.id.toString();
    }

    // Fallback to participant ID for legacy compatibility
    if (chatType === 'instagram' && selectedChat.participants?.data?.[1]?.id) {
        console.warn('getChatIdentifier: Using fallback participant ID for Instagram chat');
        return selectedChat.participants.data[1].id.toString();
    }

    if (chatType === 'messenger' && selectedChat.participants?.data?.[0]?.id) {
        console.warn('getChatIdentifier: Using fallback participant ID for Messenger chat');
        return selectedChat.participants.data[0].id.toString();
    }

    console.error('getChatIdentifier: Unable to determine chat identifier', {
        chatType,
        selectedChat: {
            id: selectedChat.id,
            sender_phone_number: selectedChat.sender_phone_number,
            participants: selectedChat.participants
        }
    });
    return null;
};

/**
 * Get sender ID for Firebase collection paths
 * This is different from getChatIdentifier - it gets the actual sender/participant ID
 * @param {Object} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @returns {string|null} - The sender ID for Firebase paths
 */
export const getSenderId = (selectedChat, chatType) => {
    if (!selectedChat) {
        console.warn('getSenderId: selectedChat is null or undefined');
        return null;
    }

    if (chatType === 'whatsapp') {
        // For WhatsApp, use the normalized phone number
        const phoneNumber = selectedChat.sender_phone_number;
        if (!phoneNumber) {
            console.warn('getSenderId: WhatsApp chat missing sender_phone_number');
            return null;
        }
        return phoneNumber.toString().trim().replace(/^\+|\s+|-/g, "");
    }

    // For Messenger/Instagram, ONLY use participant ID for Firebase messages
    // Chat ID should NOT be used for Firebase message paths - only for backend API calls
    let senderId = null;

    // ONLY use participant ID - this is what Laravel backend writes to Firebase
    if (selectedChat.participants?.data) {
        senderId = chatType === 'instagram'
            ? selectedChat.participants.data[1]?.id  // Instagram: second participant
            : selectedChat.participants.data[0]?.id; // Messenger: first participant
    }

    if (!senderId) {
        console.error('getSenderId: Could not find participant ID for Firebase messages', {
            chatType,
            chatId: selectedChat.id,
            participants: selectedChat.participants?.data,
            availableKeys: Object.keys(selectedChat),
            fullSelectedChat: selectedChat, // Add full object to see structure
            note: 'Firebase messages require participant ID, not chat ID'
        });
        return null;
    }

    return senderId.toString();
};

/**
 * Get chat ID for backend API calls and comments (NOT for Firebase messages)
 * @param {Object} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @returns {string|null} - The chat ID for backend API calls
 */
export const getChatId = (selectedChat, chatType) => {
    if (!selectedChat) {
        console.warn('getChatId: selectedChat is null or undefined');
        return null;
    }

    if (chatType === 'whatsapp') {
        // For WhatsApp, use the normalized phone number
        const phoneNumber = selectedChat.sender_phone_number;
        if (!phoneNumber) {
            console.warn('getChatId: WhatsApp chat missing sender_phone_number');
            return null;
        }
        return phoneNumber.toString().trim().replace(/^\+|\s+|-/g, "");
    }

    // For Messenger/Instagram, use the chat ID for backend API calls
    if (selectedChat.id) {
        return selectedChat.id.toString();
    }

    console.warn('getChatId: Unable to determine chat ID for backend API calls', {
        chatType,
        availableKeys: Object.keys(selectedChat),
        fullSelectedChat: selectedChat // Add full object for debugging
    });

    return null;
};

/**
 * Get consistent collection paths using the simplified page-based structure
 * Structure: ${pageId}/${sender}/messages and ${pageId}/${sender}/comments
 * @param {Object} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {string} pageId - The page identifier for organizing chats by page
 * @returns {Object} - Object containing messages and comments collection paths
 */
export const getCollectionPaths = (selectedChat, chatType, pageId) => {
    if (!selectedChat) {
        console.error('getCollectionPaths: selectedChat is required');
        return {
            messages: null,
            comments: null
        };
    }

    if (!chatType) {
        console.error('getCollectionPaths: chatType is required');
        return {
            messages: null,
            comments: null
        };
    }

    if (!pageId) {
        console.error('getCollectionPaths: pageId is required for new collection structure');
        return {
            messages: null,
            comments: null
        };
    }

    // Ensure pageId is a string
    const pageIdStr = pageId.toString();

    // Validate chatType
    const validChatTypes = ['whatsapp', 'messenger', 'instagram'];
    if (!validChatTypes.includes(chatType)) {
        console.error('getCollectionPaths: Invalid chatType', { chatType, validTypes: validChatTypes });
        return {
            messages: null,
            comments: null
        };
    }

    // Get participant ID for messages (Firebase)
    const participantId = getSenderId(selectedChat, chatType);

    // Get chat ID for comments (backend API calls)
    const chatId = getChatId(selectedChat, chatType);

    console.log('getCollectionPaths: Path determination', {
        chatType,
        participantId,
        chatId,
        selectedChatId: selectedChat.id,
        hasParticipants: !!selectedChat.participants?.data
    });

    // Messages use participant ID, comments use chat ID
    return {
        messages: participantId ? `${pageIdStr}/${participantId}/messages` : null,
        comments: chatId ? `${pageIdStr}/${chatId}/comments` : null
    };
};

/**
 * Get legacy collection paths for backward compatibility during migration
 * @param {Object} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @returns {Object} - Object containing legacy messages and comments collection paths
 */
export const getLegacyCollectionPaths = (selectedChat, chatType) => {
    if (!selectedChat || !chatType) {
        return {
            messages: null,
            comments: null
        };
    }

    if (chatType === 'whatsapp') {
        // Legacy WhatsApp paths (without page ID)
        const phoneNumber = selectedChat.sender_phone_number?.toString().trim().replace(/^\+|\s+|-/g, "");
        return {
            messages: `whatsApp/${phoneNumber}/messages`,
            comments: `whatsApp/${phoneNumber}/comments`
        };
    }

    // Legacy Messenger/Instagram paths (without page ID)
    const senderId = chatType === 'instagram'
        ? selectedChat.participants?.data?.[1]?.id
        : selectedChat.participants?.data?.[0]?.id;

    const backendChatId = selectedChat.id;

    return {
        messages: senderId ? `chats/${senderId}/messages` : null, // Very old path used sender ID
        comments: backendChatId ? `chats/${backendChatId}/comments` : null // Old path used chat ID without page ID
    };
};

/**
 * Determine if a chat requires migration from legacy to new collection structure
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @returns {boolean} - True if migration is needed
 */
export const requiresMigration = (chatType) => {
    // WhatsApp collections are already consistent, no migration needed
    if (chatType === 'whatsapp') {
        return false;
    }

    // Messenger and Instagram need migration from sender ID to backend chat ID for messages
    return chatType === 'messenger' || chatType === 'instagram';
};

/**
 * Get both current and legacy paths for dual-write scenarios during migration
 * @param {Object} selectedChat - The selected chat object
 * @param {string} chatType - The type of chat ('whatsapp', 'messenger', 'instagram')
 * @param {string} pageId - The page identifier for the new collection structure
 * @returns {Object} - Object containing both current and legacy paths
 */
export const getDualWritePaths = (selectedChat, chatType, pageId) => {
    const chatId = getChatIdentifier(selectedChat, chatType); // For backend API calls
    const senderId = getSenderId(selectedChat, chatType); // For Firebase paths
    const currentPaths = getCollectionPaths(selectedChat, chatType, pageId);
    const legacyPaths = getLegacyCollectionPaths(selectedChat, chatType);

    return {
        current: currentPaths,
        legacy: legacyPaths,
        chatId, // Original chat ID for backend API calls
        senderId, // Sender ID for Firebase paths
        pageId,
        requiresMigration: true // Always require migration to new page-based structure
    };
};

/**
 * Validate collection path parameters
 * @param {string} chatId - The chat identifier
 * @param {string} chatType - The type of chat
 * @param {string} pageId - The page identifier
 * @returns {Object} - Validation result with isValid flag and errors array
 */
export const validateCollectionParams = (chatId, chatType, pageId) => {
    const errors = [];

    if (!chatId || typeof chatId !== 'string' || chatId.trim() === '') {
        errors.push('chatId must be a non-empty string');
    }

    if (!chatType || typeof chatType !== 'string') {
        errors.push('chatType must be a string');
    } else {
        const validChatTypes = ['whatsapp', 'messenger', 'instagram'];
        if (!validChatTypes.includes(chatType)) {
            errors.push(`chatType must be one of: ${validChatTypes.join(', ')}`);
        }
    }

    if (!pageId || typeof pageId !== 'string' || pageId.trim() === '') {
        errors.push('pageId must be a non-empty string');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Helper function to extract chat type from selected chat object
 * @param {Object} selectedChat - The selected chat object
 * @param {string} activeFilter - The active filter from UI ('whatsapp' or other)
 * @returns {string|null} - The determined chat type
 */
/**
 * Get the collection path for all chats belonging to a specific page
 * @param {string} pageId - The page identifier
 * @returns {string} - The collection path for all chats in the page
 */
export const getPageChatsCollectionPath = (pageId) => {
    if (!pageId) {
        console.error('getPageChatsCollectionPath: pageId is required');
        return null;
    }

    return `chats/${pageId}`;
};

/**
 * Get collection paths for a specific chat within a page
 * @param {string} pageId - The page identifier
 * @param {string} chatId - The chat identifier
 * @returns {Object} - Object containing the chat document path and subcollections
 */
export const getChatCollectionPaths = (pageId, chatId) => {
    if (!pageId || !chatId) {
        console.error('getChatCollectionPaths: Both pageId and chatId are required');
        return {
            chatDocument: null,
            messages: null,
            comments: null
        };
    }

    // Create a combined document ID to maintain valid Firebase collection structure
    const combinedChatId = `${pageId}_${chatId}`;

    return {
        chatDocument: `chats/${combinedChatId}`,
        messages: `chats/${combinedChatId}/messages`,
        comments: `chats/${combinedChatId}/comments`
    };
};

export const determineChatType = (selectedChat, activeFilter = null) => {
    if (!selectedChat) {
        return null;
    }

    // If activeFilter is explicitly 'whatsapp', use that
    if (activeFilter === 'whatsapp') {
        return 'whatsapp';
    }

    // Check for WhatsApp indicators in the chat object
    if (selectedChat.sender_phone_number) {
        return 'whatsapp';
    }

    // Check for Instagram flag
    if (selectedChat.flage === 'instagram') {
        return 'instagram';
    }

    // Default to messenger for other cases
    if (selectedChat.participants?.data) {
        return 'messenger';
    }

    console.warn('determineChatType: Unable to determine chat type', { selectedChat, activeFilter });
    return null;
};
