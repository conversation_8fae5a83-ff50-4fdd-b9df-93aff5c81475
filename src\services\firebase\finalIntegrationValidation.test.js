/**
 * Final Integration Testing and Validation for Firebase Collection Consistency
 *
 * This comprehensive test suite validates that the Firebase collection consistency
 * implementation works correctly across all systems and scenarios.
 *
 * Test Coverage:
 * - Message sending and receiving with new chat ID paths
 * - Comment functionality with shared chat ID usage
 * - Real-time listeners with chat ID-based collection structure
 * - Backward compatibility during transition from sender ID to chat ID
 * - Identical chat ID resolution logic between messages and comments
 * - Error handling and edge cases
 * - Performance and scalability
 * - WhatsApp integration consistency
 * - Migration scenarios
 */

import { configureStore } from '@reduxjs/toolkit';
import metaBusinessChatSlice, {
    sendMessage,
    fetchMessages,
    fetchWhatsAppMessages
} from '../../redux/features/metaBusinessChatSlice';

// Mock Firebase Firestore
jest.mock('firebase/firestore', () => ({
    collection: jest.fn(),
    doc: jest.fn(),
    setDoc: jest.fn(),
    getDocs: jest.fn(),
    onSnapshot: jest.fn(),
    query: jest.fn(),
    orderBy: jest.fn(),
    where: jest.fn(),
    addDoc: jest.fn(),
    updateDoc: jest.fn(),
    deleteDoc: jest.fn(),
    getDoc: jest.fn(),
    serverTimestamp: jest.fn(() => ({ seconds: Date.now() / 1000 })),
    arrayUnion: jest.fn(value => ({ arrayUnion: value }))
}));

// Mock Firebase config
jest.mock('../../utils/firebase.config', () => ({
    db: {}
}));

// Mock meta service
jest.mock('../../services/integrations/meta', () => ({
    default: {
        sendMessageApi: jest.fn(),
        getMessagesForChatApi: jest.fn(),
        getMessagesForWhatsappChatApi: jest.fn(),
        sendWhatsappMessageApi: jest.fn()
    }
}));

// Mock comment services
jest.mock('../comments/index', () => ({
    addComment: jest.fn(),
    fetchComments: jest.fn(),
    listenToComments: jest.fn(),
    markCommentsAsRead: jest.fn(),
    initializeCommentsCollection: jest.fn()
}));

// Mock collection paths service
jest.mock('./collectionPaths', () => ({
    getDualWritePaths: jest.fn(),
    determineChatType: jest.fn(),
    getChatIdentifier: jest.fn(),
    getCollectionPaths: jest.fn(),
    getLegacyCollectionPaths: jest.fn(),
    requiresMigration: jest.fn(),
    validateCollectionParams: jest.fn()
}));

import {
    collection,
    doc,
    setDoc,
    getDocs,
    onSnapshot,
    query,
    orderBy,
    addDoc,
    getDoc,
    updateDoc,
    serverTimestamp,
    arrayUnion
} from 'firebase/firestore';

import metaService from '../../services/integrations/meta';
import {
    addComment,
    fetchComments,
    listenToComments,
    markCommentsAsRead,
    initializeCommentsCollection
} from '../comments/index';

import {
    getDualWritePaths,
    determineChatType,
    getChatIdentifier,
    getCollectionPaths,
    getLegacyCollectionPaths,
    requiresMigration,
    validateCollectionParams
} from './collectionPaths';

describe('Final Integration Testing and Validation', () => {
    let store;

    beforeEach(() => {
        store = configureStore({
            reducer: {
                metaBusinessSuite: metaBusinessChatSlice
            }
        });
        jest.clearAllMocks();
    });

    describe('1. Message Sending and Receiving with New Chat ID Paths', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const mockSelectedPage = {
            id: 'page_123',
            page_token: 'token_123'
        };

        const sharedChatId = 'backend_chat_123';

        beforeEach(() => {
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            getCollectionPaths.mockReturnValue({
                messages: `chats/${sharedChatId}/messages`,
                comments: `chats/${sharedChatId}/comments`
            });
            getDualWritePaths.mockReturnValue({
                current: {
                    messages: `chats/${sharedChatId}/messages`,
                    comments: `chats/${sharedChatId}/comments`
                },
                legacy: {
                    messages: 'chats/participant_456/messages',
                    comments: `chats/${sharedChatId}/comments`
                },
                chatId: sharedChatId,
                requiresMigration: true
            });
            requiresMigration.mockReturnValue(true);
            validateCollectionParams.mockReturnValue({ isValid: true, errors: [] });
        });

        test('sends messages to correct chat ID-based collection path', async () => {
            metaService.sendMessageApi.mockResolvedValue({
                message_id: 'api_msg_123'
            });
            setDoc.mockResolvedValue();

            const action = sendMessage({
                message: 'Hello World',
                selectedChat: mockSelectedChat,
                selectedPage: mockSelectedPage
            });

            await store.dispatch(action);

            // Verify message was written to chat ID-based path
            expect(doc).toHaveBeenCalledWith(
                expect.anything(),
                'chats',
                sharedChatId,
                'messages',
                expect.any(String)
            );

            // Verify dual write occurred (both current and legacy paths)
            expect(setDoc).toHaveBeenCalledTimes(2);
        });

        test('receives messages from correct chat ID-based collection path', async () => {
            const mockMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Hello',
                        sender: sharedChatId,
                        created_time: '2024-01-01T10:00:00Z',
                        type: 'text'
                    })
                }
            ];

            metaService.getMessagesForChatApi.mockResolvedValue([]);
            getDocs.mockResolvedValue({ docs: mockMessages });
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Set up state
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const action = fetchMessages({ thread: mockSelectedChat });
            const result = await store.dispatch(action);

            expect(result.type).toBe('metaBusinessSuite/fetchMessages/fulfilled');
            expect(result.payload.activeCollectionPath).toBe(`chats/${sharedChatId}/messages`);
            expect(result.payload.messages).toHaveLength(1);

            // Verify correct collection path was used
            expect(collection).toHaveBeenCalledWith(
                expect.anything(),
                'chats',
                sharedChatId,
                'messages'
            );
        });

        test('real-time listeners use chat ID-based collection paths', async () => {
            const mockMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Real-time message',
                        sender: sharedChatId,
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            metaService.getMessagesForChatApi.mockResolvedValue([]);
            getDocs.mockResolvedValue({ docs: mockMessages });

            // Mock onSnapshot to simulate real-time updates
            onSnapshot.mockImplementation((query, callback) => {
                callback({
                    docs: mockMessages,
                    docChanges: () => [{ type: 'added', doc: mockMessages[0] }]
                });
                return jest.fn();
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Set up state
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const action = fetchMessages({ thread: mockSelectedChat });
            await store.dispatch(action);

            // Verify listener was set up with correct chat ID-based path
            expect(collection).toHaveBeenCalledWith(
                expect.anything(),
                'chats',
                sharedChatId,
                'messages'
            );
            expect(onSnapshot).toHaveBeenCalled();
        });
    });

    describe('2. Comment Functionality with Shared Chat ID Usage', () => {
        const sharedChatId = 'backend_chat_123';
        const chatType = 'messenger';

        beforeEach(() => {
            determineChatType.mockReturnValue(chatType);
            getChatIdentifier.mockReturnValue(sharedChatId);
            getCollectionPaths.mockReturnValue({
                messages: `chats/${sharedChatId}/messages`,
                comments: `chats/${sharedChatId}/comments`
            });
            validateCollectionParams.mockReturnValue({ isValid: true, errors: [] });
        });

        test('adds comments using shared chat ID', async () => {
            const commentText = 'This is a test comment';
            const author = { id: 'user_123', name: 'Test User', email: '<EMAIL>' };

            addComment.mockResolvedValue('comment_123');

            const result = await addComment(sharedChatId, chatType, commentText, author);

            expect(result).toBe('comment_123');
            expect(addComment).toHaveBeenCalledWith(sharedChatId, chatType, commentText, author);
        });

        test('fetches comments using shared chat ID', async () => {
            const mockComments = [
                {
                    id: 'comment_1',
                    text: 'First comment',
                    author: { id: 'user_123', name: 'Test User' },
                    createdAt: '2024-01-01T10:00:00Z',
                    chatType
                }
            ];

            fetchComments.mockResolvedValue(mockComments);

            const result = await fetchComments(sharedChatId, chatType);

            expect(result).toEqual(mockComments);
            expect(fetchComments).toHaveBeenCalledWith(sharedChatId, chatType);
        });

        test('listens to comments using shared chat ID', async () => {
            const mockCallback = jest.fn();
            const mockUnsubscribe = jest.fn();

            listenToComments.mockReturnValue(mockUnsubscribe);

            const unsubscribe = listenToComments(sharedChatId, chatType, mockCallback);

            expect(unsubscribe).toBe(mockUnsubscribe);
            expect(listenToComments).toHaveBeenCalledWith(sharedChatId, chatType, mockCallback);
        });

        test('marks comments as read using shared chat ID', async () => {
            const commentIds = ['comment_1', 'comment_2'];
            const user = { id: 'user_123', name: 'Test User' };

            markCommentsAsRead.mockResolvedValue();

            await markCommentsAsRead(sharedChatId, chatType, commentIds, user);

            expect(markCommentsAsRead).toHaveBeenCalledWith(sharedChatId, chatType, commentIds, user);
        });
    });

    describe('3. Messages and Comments Use Identical Chat ID Resolution Logic', () => {
        const testCases = [
            {
                name: 'Messenger chat',
                chat: {
                    id: 'messenger_chat_123',
                    participants: { data: [{ id: 'participant_456' }] }
                },
                expectedChatType: 'messenger',
                expectedChatId: 'messenger_chat_123'
            },
            {
                name: 'Instagram chat',
                chat: {
                    id: 'instagram_chat_456',
                    flage: 'instagram',
                    participants: { data: [{ id: 'sender_123' }, { id: 'participant_789' }] }
                },
                expectedChatType: 'instagram',
                expectedChatId: 'instagram_chat_456'
            },
            {
                name: 'WhatsApp chat',
                chat: {
                    sender_phone_number: '+1234567890'
                },
                expectedChatType: 'whatsapp',
                expectedChatId: '1234567890'
            }
        ];

        testCases.forEach(({ name, chat, expectedChatType, expectedChatId }) => {
            test(`${name} uses identical chat ID resolution for messages and comments`, async () => {
                determineChatType.mockReturnValue(expectedChatType);
                getChatIdentifier.mockReturnValue(expectedChatId);
                getCollectionPaths.mockReturnValue({
                    messages: expectedChatType === 'whatsapp'
                        ? `whatsApp/${expectedChatId}/messages`
                        : `chats/${expectedChatId}/messages`,
                    comments: expectedChatType === 'whatsapp'
                        ? `whatsApp/${expectedChatId}/comments`
                        : `chats/${expectedChatId}/comments`
                });

                // Test message system
                metaService.sendMessageApi.mockResolvedValue({ message_id: 'msg_123' });
                setDoc.mockResolvedValue();

                const messageAction = sendMessage({
                    message: 'Test message',
                    selectedChat: chat,
                    selectedPage: { id: 'page_123', page_token: 'token_123' }
                });

                await store.dispatch(messageAction);

                // Test comment system
                addComment.mockResolvedValue('comment_123');
                await addComment(expectedChatId, expectedChatType, 'Test comment', {
                    id: 'user_123',
                    name: 'Test User',
                    email: '<EMAIL>'
                });

                // Verify both systems used the same chat type determination
                expect(determineChatType).toHaveBeenCalledWith(chat);

                // Verify both systems used the same chat ID
                expect(getChatIdentifier).toHaveBeenCalledWith(chat, expectedChatType);

                // Verify both systems used the same collection paths
                expect(getCollectionPaths).toHaveBeenCalledWith(expectedChatId, expectedChatType, expect.anything());
            });
        });
    });

    describe('4. Backward Compatibility During Transition', () => {
        const mockSelectedChat = {
            id: 'backend_chat_123',
            participants: {
                data: [{ id: 'participant_456' }]
            }
        };

        const sharedChatId = 'backend_chat_123';

        beforeEach(() => {
            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            getDualWritePaths.mockReturnValue({
                current: {
                    messages: `chats/${sharedChatId}/messages`,
                    comments: `chats/${sharedChatId}/comments`
                },
                legacy: {
                    messages: 'chats/participant_456/messages',
                    comments: `chats/${sharedChatId}/comments`
                },
                chatId: sharedChatId,
                requiresMigration: true
            });
            requiresMigration.mockReturnValue(true);
        });

        test('dual write maintains backward compatibility', async () => {
            metaService.sendMessageApi.mockResolvedValue({ message_id: 'msg_123' });
            setDoc.mockResolvedValue();

            const action = sendMessage({
                message: 'Backward compatibility test',
                selectedChat: mockSelectedChat,
                selectedPage: { id: 'page_123', page_token: 'token_123' }
            });

            await store.dispatch(action);

            // Verify dual write to both current and legacy paths
            expect(setDoc).toHaveBeenCalledTimes(2);

            // Verify current path (chat ID-based)
            expect(doc).toHaveBeenCalledWith(
                expect.anything(),
                'chats',
                sharedChatId,
                'messages',
                expect.any(String)
            );

            // Verify legacy path (sender ID-based)
            expect(doc).toHaveBeenCalledWith(
                expect.anything(),
                'chats',
                'participant_456',
                'messages',
                expect.any(String)
            );
        });

        test('fallback read logic works correctly', async () => {
            // Mock scenario: new path empty, legacy path has messages
            const mockLegacyMessages = [
                {
                    id: 'legacy_msg1',
                    data: () => ({
                        message: 'Legacy message',
                        sender: 'participant_456',
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            metaService.getMessagesForChatApi.mockResolvedValue([]);
            getDocs.mockResolvedValueOnce({ docs: [] }) // New path empty
                .mockResolvedValueOnce({ docs: mockLegacyMessages }); // Legacy path has data

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Set up state
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const action = fetchMessages({ thread: mockSelectedChat });
            const result = await store.dispatch(action);

            expect(result.type).toBe('metaBusinessSuite/fetchMessages/fulfilled');
            expect(result.payload.activeCollectionPath).toBe('chats/participant_456/messages');
            expect(result.payload.messages).toHaveLength(1);

            // Verify both paths were attempted
            expect(getDocs).toHaveBeenCalledTimes(2);
        });

        test('comments maintain consistency during migration', async () => {
            // Comments should always use the new chat ID-based path
            const mockComments = [
                {
                    id: 'comment_1',
                    text: 'Migration test comment',
                    author: { id: 'user_123', name: 'Test User' },
                    createdAt: '2024-01-01T10:00:00Z'
                }
            ];

            fetchComments.mockResolvedValue(mockComments);

            const result = await fetchComments(sharedChatId, 'messenger');

            expect(result).toEqual(mockComments);
            expect(fetchComments).toHaveBeenCalledWith(sharedChatId, 'messenger');

            // Comments should not use legacy paths - they already use chat ID
            expect(fetchComments).not.toHaveBeenCalledWith('participant_456', 'messenger');
        });
    });

    describe('5. WhatsApp Integration Consistency', () => {
        const mockWhatsAppChat = {
            sender_phone_number: '****** 567 8900'
        };

        const mockSelectedPhone = {
            id: 'phone_123',
            display_phone_number: '+0987654321'
        };

        const normalizedPhoneNumber = '12345678900';

        beforeEach(() => {
            determineChatType.mockReturnValue('whatsapp');
            getChatIdentifier.mockReturnValue(normalizedPhoneNumber);
            getCollectionPaths.mockReturnValue({
                messages: `whatsApp/${normalizedPhoneNumber}/messages`,
                comments: `whatsApp/${normalizedPhoneNumber}/comments`
            });
            requiresMigration.mockReturnValue(false); // WhatsApp doesn't need migration
        });

        test('WhatsApp messages use consistent phone number normalization', async () => {
            const mockApiMessages = [
                {
                    id: 'whatsapp_msg_1',
                    message: 'Hello WhatsApp',
                    from: normalizedPhoneNumber,
                    created_at: '2024-01-01T10:00:00Z',
                    type: 'text'
                }
            ];

            metaService.getMessagesForWhatsappChatApi.mockResolvedValue({
                data: mockApiMessages
            });

            onSnapshot.mockImplementation((query, callback) => {
                callback({ docs: [], docChanges: () => [] });
                return jest.fn();
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            const action = fetchWhatsAppMessages({
                thread: mockWhatsAppChat,
                selectedPhone: mockSelectedPhone
            });

            const result = await store.dispatch(action);

            expect(result.type).toBe('metaBusinessSuite/fetchWhatsAppMessages/fulfilled');
            expect(result.payload.apiMessages).toHaveLength(1);

            // Verify consistent path was used
            expect(collection).toHaveBeenCalledWith(
                expect.anything(),
                'whatsApp',
                normalizedPhoneNumber,
                'messages'
            );
        });

        test('WhatsApp comments use same normalized phone number', async () => {
            const mockComments = [
                {
                    id: 'whatsapp_comment_1',
                    text: 'WhatsApp comment',
                    author: { id: 'user_123', name: 'Test User' },
                    createdAt: '2024-01-01T10:00:00Z',
                    chatType: 'whatsapp'
                }
            ];

            fetchComments.mockResolvedValue(mockComments);

            const result = await fetchComments(normalizedPhoneNumber, 'whatsapp');

            expect(result).toEqual(mockComments);
            expect(fetchComments).toHaveBeenCalledWith(normalizedPhoneNumber, 'whatsapp');

            // Verify both messages and comments use the same normalized phone number
            expect(getChatIdentifier).toHaveBeenCalledWith(mockWhatsAppChat, 'whatsapp');
        });

        test('phone number normalization handles various formats consistently', () => {
            const phoneNumberVariations = [
                '****** 567 8900',
                '1234567890',
                '******-567-8900',
                '  +1234567890  '
            ];

            const expectedNormalized = '12345678900';

            phoneNumberVariations.forEach(phoneNumber => {
                const chat = { sender_phone_number: phoneNumber };

                getChatIdentifier.mockReturnValue(expectedNormalized);
                getCollectionPaths.mockReturnValue({
                    messages: `whatsApp/${expectedNormalized}/messages`,
                    comments: `whatsApp/${expectedNormalized}/comments`
                });

                const chatId = getChatIdentifier(chat, 'whatsapp');
                const paths = getCollectionPaths(chatId, 'whatsapp', chat);

                expect(chatId).toBe(expectedNormalized);
                expect(paths.messages).toBe(`whatsApp/${expectedNormalized}/messages`);
                expect(paths.comments).toBe(`whatsApp/${expectedNormalized}/comments`);
            });
        });
    });

    describe('6. Error Handling and Edge Cases', () => {
        test('handles Firebase errors consistently across systems', async () => {
            const firebaseError = new Error('Firebase connection failed');
            const sharedChatId = 'backend_chat_123';

            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);

            // Mock Firebase errors for both systems
            setDoc.mockRejectedValue(firebaseError);
            addComment.mockRejectedValue(firebaseError);

            // Test message system error handling
            metaService.sendMessageApi.mockResolvedValue({ message_id: 'msg_123' });

            const messageAction = sendMessage({
                message: 'Test message',
                selectedChat: { id: sharedChatId, participants: { data: [{ id: 'participant_456' }] } },
                selectedPage: { id: 'page_123', page_token: 'token_123' }
            });

            const messageResult = await store.dispatch(messageAction);
            expect(messageResult.type).toBe('metaBusinessSuite/sendMessage/rejected');

            // Test comment system error handling
            let commentError;
            try {
                await addComment(sharedChatId, 'messenger', 'Test comment', {
                    id: 'user_123',
                    name: 'Test User',
                    email: '<EMAIL>'
                });
            } catch (error) {
                commentError = error;
            }

            expect(commentError).toBeDefined();
            expect(commentError.message).toBe('Firebase connection failed');
        });

        test('handles malformed chat objects gracefully', async () => {
            const malformedChat = {}; // Missing required fields

            determineChatType.mockReturnValue(null);
            getChatIdentifier.mockReturnValue(null);
            validateCollectionParams.mockReturnValue({
                isValid: false,
                errors: ['chatId must be a non-empty string']
            });

            // Test message system with malformed chat
            const messageAction = sendMessage({
                message: 'Test message',
                selectedChat: malformedChat,
                selectedPage: { id: 'page_123', page_token: 'token_123' }
            });

            const messageResult = await store.dispatch(messageAction);
            expect(messageResult.type).toBe('metaBusinessSuite/sendMessage/rejected');

            // Test comment system with malformed chat
            let commentError;
            try {
                await addComment(null, null, 'Test comment', {
                    id: 'user_123',
                    name: 'Test User',
                    email: '<EMAIL>'
                });
            } catch (error) {
                commentError = error;
            }

            expect(commentError).toBeDefined();
        });

        test('handles network connectivity issues', async () => {
            const networkError = new Error('Network request failed');
            const sharedChatId = 'backend_chat_123';

            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);

            // Mock network errors
            metaService.sendMessageApi.mockRejectedValue(networkError);
            fetchComments.mockRejectedValue(networkError);

            // Test message system network error
            const messageAction = sendMessage({
                message: 'Test message',
                selectedChat: { id: sharedChatId, participants: { data: [{ id: 'participant_456' }] } },
                selectedPage: { id: 'page_123', page_token: 'token_123' }
            });

            const messageResult = await store.dispatch(messageAction);
            expect(messageResult.type).toBe('metaBusinessSuite/sendMessage/rejected');

            // Test comment system network error (should return empty array)
            const commentResult = await fetchComments(sharedChatId, 'messenger');
            expect(commentResult).toEqual([]);
        });
    });

    describe('7. Performance and Scalability', () => {
        test('handles concurrent operations efficiently', async () => {
            const sharedChatId = 'backend_chat_123';
            const mockSelectedChat = {
                id: sharedChatId,
                participants: { data: [{ id: 'participant_456' }] }
            };

            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            getDualWritePaths.mockReturnValue({
                current: {
                    messages: `chats/${sharedChatId}/messages`,
                    comments: `chats/${sharedChatId}/comments`
                },
                legacy: {
                    messages: 'chats/participant_456/messages',
                    comments: `chats/${sharedChatId}/comments`
                },
                chatId: sharedChatId,
                requiresMigration: true
            });

            metaService.sendMessageApi.mockResolvedValue({ message_id: 'msg_123' });
            setDoc.mockResolvedValue();
            addComment.mockResolvedValue('comment_123');

            // Perform concurrent operations
            const operations = [
                store.dispatch(sendMessage({
                    message: 'Message 1',
                    selectedChat: mockSelectedChat,
                    selectedPage: { id: 'page_123', page_token: 'token_123' }
                })),
                store.dispatch(sendMessage({
                    message: 'Message 2',
                    selectedChat: mockSelectedChat,
                    selectedPage: { id: 'page_123', page_token: 'token_123' }
                })),
                addComment(sharedChatId, 'messenger', 'Comment 1', {
                    id: 'user_123',
                    name: 'Test User',
                    email: '<EMAIL>'
                }),
                addComment(sharedChatId, 'messenger', 'Comment 2', {
                    id: 'user_123',
                    name: 'Test User',
                    email: '<EMAIL>'
                })
            ];

            const results = await Promise.all(operations);

            // All operations should succeed
            expect(results[0].type).toBe('metaBusinessSuite/sendMessage/fulfilled');
            expect(results[1].type).toBe('metaBusinessSuite/sendMessage/fulfilled');
            expect(results[2]).toBe('comment_123');
            expect(results[3]).toBe('comment_123');

            // Verify appropriate Firebase calls were made
            expect(setDoc).toHaveBeenCalledTimes(4); // 2 messages × 2 paths each
            expect(addComment).toHaveBeenCalledTimes(2);
        });

        test('optimizes query performance with proper indexing', async () => {
            const sharedChatId = 'backend_chat_123';
            const mockSelectedChat = {
                id: sharedChatId,
                participants: { data: [{ id: 'participant_456' }] }
            };

            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            getCollectionPaths.mockReturnValue({
                messages: `chats/${sharedChatId}/messages`,
                comments: `chats/${sharedChatId}/comments`
            });

            const mockMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Message 1',
                        sender: sharedChatId,
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            metaService.getMessagesForChatApi.mockResolvedValue([]);
            getDocs.mockResolvedValue({ docs: mockMessages });
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Set up state
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const startTime = performance.now();
            const action = fetchMessages({ thread: mockSelectedChat });
            await store.dispatch(action);
            const endTime = performance.now();

            // Verify query was optimized with proper ordering
            expect(orderBy).toHaveBeenCalledWith('created_time', 'asc');

            // Performance should be reasonable (this is a basic check)
            const executionTime = endTime - startTime;
            expect(executionTime).toBeLessThan(1000); // Should complete within 1 second
        });
    });

    describe('8. Real-time Listener Validation', () => {
        test('real-time listeners maintain consistency across systems', async () => {
            const sharedChatId = 'backend_chat_123';
            const mockSelectedChat = {
                id: sharedChatId,
                participants: { data: [{ id: 'participant_456' }] }
            };

            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            getCollectionPaths.mockReturnValue({
                messages: `chats/${sharedChatId}/messages`,
                comments: `chats/${sharedChatId}/comments`
            });

            const mockMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Real-time message',
                        sender: sharedChatId,
                        created_time: '2024-01-01T10:00:00Z'
                    })
                }
            ];

            const mockComments = [
                {
                    id: 'comment1',
                    text: 'Real-time comment',
                    author: { id: 'user_123', name: 'Test User' },
                    createdAt: '2024-01-01T10:01:00Z'
                }
            ];

            // Mock message listener
            metaService.getMessagesForChatApi.mockResolvedValue([]);
            getDocs.mockResolvedValue({ docs: mockMessages });
            onSnapshot.mockImplementation((query, callback) => {
                callback({
                    docs: mockMessages,
                    docChanges: () => [{ type: 'added', doc: mockMessages[0] }]
                });
                return jest.fn();
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Mock comment listener
            listenToComments.mockImplementation((chatId, chatType, callback) => {
                callback(mockComments);
                return jest.fn();
            });

            // Set up message listener
            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            await store.dispatch(fetchMessages({ thread: mockSelectedChat }));

            // Set up comment listener
            const mockCallback = jest.fn();
            listenToComments(sharedChatId, 'messenger', mockCallback);

            // Verify both listeners used the same chat ID
            expect(collection).toHaveBeenCalledWith(
                expect.anything(),
                'chats',
                sharedChatId,
                'messages'
            );

            expect(listenToComments).toHaveBeenCalledWith(
                sharedChatId,
                'messenger',
                expect.any(Function)
            );
        });

        test('listeners handle connection drops gracefully', async () => {
            const sharedChatId = 'backend_chat_123';

            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);

            // Mock listener that fails
            onSnapshot.mockImplementation((query, callback, errorCallback) => {
                errorCallback(new Error('Connection lost'));
                return jest.fn();
            });

            listenToComments.mockImplementation((chatId, chatType, callback) => {
                callback([], new Error('Connection lost'));
                return jest.fn();
            });

            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            // Test message listener error handling
            metaService.getMessagesForChatApi.mockResolvedValue([]);
            getDocs.mockResolvedValue({ docs: [] });

            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const messageAction = fetchMessages({
                thread: { id: sharedChatId, participants: { data: [{ id: 'participant_456' }] } }
            });

            const result = await store.dispatch(messageAction);
            expect(result.type).toBe('metaBusinessSuite/fetchMessages/fulfilled');

            // Test comment listener error handling
            const mockCallback = jest.fn();
            listenToComments(sharedChatId, 'messenger', mockCallback);

            expect(mockCallback).toHaveBeenCalledWith([], expect.any(Error));
        });
    });

    describe('9. Data Integrity Validation', () => {
        test('ensures data consistency between message and comment systems', async () => {
            const sharedChatId = 'backend_chat_123';
            const mockSelectedChat = {
                id: sharedChatId,
                participants: { data: [{ id: 'participant_456' }] }
            };

            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            getCollectionPaths.mockReturnValue({
                messages: `chats/${sharedChatId}/messages`,
                comments: `chats/${sharedChatId}/comments`
            });

            // Mock data from both systems
            const mockMessages = [
                {
                    id: 'msg1',
                    data: () => ({
                        message: 'Hello',
                        sender: sharedChatId,
                        created_time: '2024-01-01T10:00:00Z',
                        type: 'text'
                    })
                }
            ];

            const mockComments = [
                {
                    id: 'comment1',
                    text: 'This is a comment',
                    author: { id: 'user_123', name: 'Test User' },
                    createdAt: '2024-01-01T10:01:00Z',
                    chatType: 'messenger'
                }
            ];

            // Fetch messages
            metaService.getMessagesForChatApi.mockResolvedValue([]);
            getDocs.mockResolvedValue({ docs: mockMessages });
            collection.mockReturnValue({});
            query.mockReturnValue({});
            orderBy.mockReturnValue({});

            store.dispatch({
                type: 'metaBusinessSuite/setLatestMessages',
                payload: [{ id: 'participant_456', sender: 'participant_456' }]
            });

            const messageResult = await store.dispatch(fetchMessages({ thread: mockSelectedChat }));

            // Fetch comments
            fetchComments.mockResolvedValue(mockComments);
            const commentResult = await fetchComments(sharedChatId, 'messenger');

            // Verify both operations used the same chat ID
            expect(messageResult.payload.activeCollectionPath).toContain(sharedChatId);
            expect(fetchComments).toHaveBeenCalledWith(sharedChatId, 'messenger');

            // Verify data integrity
            expect(messageResult.payload.messages).toHaveLength(1);
            expect(commentResult).toHaveLength(1);

            // Both should reference the same logical chat
            const messageData = messageResult.payload.messages[0];
            const commentData = commentResult[0];

            expect(messageData.from.id).toBeDefined();
            expect(commentData.chatType).toBe('messenger');
        });

        test('validates collection path consistency', () => {
            const testCases = [
                {
                    chatId: 'messenger_chat_123',
                    chatType: 'messenger',
                    expectedMessages: 'chats/messenger_chat_123/messages',
                    expectedComments: 'chats/messenger_chat_123/comments'
                },
                {
                    chatId: 'instagram_chat_456',
                    chatType: 'instagram',
                    expectedMessages: 'chats/instagram_chat_456/messages',
                    expectedComments: 'chats/instagram_chat_456/comments'
                },
                {
                    chatId: '1234567890',
                    chatType: 'whatsapp',
                    expectedMessages: 'whatsApp/1234567890/messages',
                    expectedComments: 'whatsApp/1234567890/comments'
                }
            ];

            testCases.forEach(({ chatId, chatType, expectedMessages, expectedComments }) => {
                getCollectionPaths.mockReturnValue({
                    messages: expectedMessages,
                    comments: expectedComments
                });

                const paths = getCollectionPaths(chatId, chatType);

                expect(paths.messages).toBe(expectedMessages);
                expect(paths.comments).toBe(expectedComments);

                // Verify both paths use the same chat ID
                const messagesChatId = paths.messages.split('/')[1];
                const commentsChatId = paths.comments.split('/')[1];
                expect(messagesChatId).toBe(commentsChatId);
                expect(messagesChatId).toBe(chatId);
            });
        });
    });

    describe('10. Migration Scenario Validation', () => {
        test('validates migration state consistency', async () => {
            const sharedChatId = 'backend_chat_123';
            const mockSelectedChat = {
                id: sharedChatId,
                participants: { data: [{ id: 'participant_456' }] }
            };

            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            requiresMigration.mockReturnValue(true);
            getDualWritePaths.mockReturnValue({
                current: {
                    messages: `chats/${sharedChatId}/messages`,
                    comments: `chats/${sharedChatId}/comments`
                },
                legacy: {
                    messages: 'chats/participant_456/messages',
                    comments: `chats/${sharedChatId}/comments`
                },
                chatId: sharedChatId,
                requiresMigration: true
            });

            // Test that migration requirements are consistent
            expect(requiresMigration('messenger')).toBe(true);
            expect(requiresMigration('instagram')).toBe(true);
            expect(requiresMigration('whatsapp')).toBe(false);

            // Test dual write paths are correctly structured
            const dualPaths = getDualWritePaths(mockSelectedChat, 'messenger');
            expect(dualPaths.requiresMigration).toBe(true);
            expect(dualPaths.current.messages).toContain(sharedChatId);
            expect(dualPaths.legacy.messages).toContain('participant_456');
            expect(dualPaths.current.comments).toBe(dualPaths.legacy.comments); // Comments already consistent
        });

        test('validates post-migration cleanup state', async () => {
            const sharedChatId = 'backend_chat_123';

            determineChatType.mockReturnValue('messenger');
            getChatIdentifier.mockReturnValue(sharedChatId);
            requiresMigration.mockReturnValue(false); // Post-migration state
            getCollectionPaths.mockReturnValue({
                messages: `chats/${sharedChatId}/messages`,
                comments: `chats/${sharedChatId}/comments`
            });

            // In post-migration state, only new paths should be used
            const paths = getCollectionPaths(sharedChatId, 'messenger');
            expect(paths.messages).toBe(`chats/${sharedChatId}/messages`);
            expect(paths.comments).toBe(`chats/${sharedChatId}/comments`);

            // Legacy paths should not be used
            getLegacyCollectionPaths.mockReturnValue({
                messages: null,
                comments: null
            });

            const legacyPaths = getLegacyCollectionPaths({ id: sharedChatId }, 'messenger');
            expect(legacyPaths.messages).toBeNull();
        });
    });
});
