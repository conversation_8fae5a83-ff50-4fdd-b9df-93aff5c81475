/**
 * Message Flow Diagnostic Utility
 *
 * This utility helps diagnose issues with message sending and receiving
 * in the Firebase collection consistency implementation.
 */

import { getDualWritePaths, determineChatType, getChatIdentifier } from '../services/firebase/collectionPaths';

/**
 * Comprehensive diagnostic for message flow issues
 * @param {Object} store - Redux store
 * @param {Object} selectedChat - Current selected chat
 * @returns {Object} Diagnostic results
 */
export const diagnoseMessageFlow = (store, selectedChat = null) => {
    const state = store.getState().metaBusinessSuite;
    const chat = selectedChat || state.selectedChat;
    const selectedPage = state.selectedPage;

    console.group('🔍 MESSAGE FLOW DIAGNOSTIC');

    const diagnostic = {
        timestamp: new Date().toISOString(),
        issues: [],
        warnings: [],
        recommendations: [],
        state: {
            hasSelectedChat: !!chat,
            hasSelectedPage: !!selectedPage,
            latestMessagesCount: state.latestMessages?.length || 0,
            messagesCount: state.messages?.length || 0,
            activeFilter: state.activeFilter,
            loading: state.loading
        }
    };

    // Check 1: Selected Chat
    if (!chat) {
        diagnostic.issues.push('No selected chat - messages cannot be processed');
        console.error('❌ No selected chat');
    } else {
        console.log('✅ Selected chat:', {
            id: chat.id,
            type: chat.sender_phone_number ? 'whatsapp' :
                chat.flage === 'instagram' ? 'instagram' : 'messenger',
            participants: chat.participants?.data?.length || 0
        });
    }

    // Check 2: Selected Page
    if (!selectedPage?.id) {
        diagnostic.issues.push('No selected page - latest messages listener may not work');
        console.error('❌ No selected page');
    } else {
        console.log('✅ Selected page:', selectedPage.id);
    }

    // Check 3: Collection Path Resolution
    if (chat) {
        try {
            const chatType = determineChatType(chat);
            const chatId = getChatIdentifier(chat, chatType);
            const dualWritePaths = getDualWritePaths(chat, chatType);

            console.log('📍 Collection Paths:', {
                chatType,
                chatId,
                currentPath: dualWritePaths.current.messages,
                legacyPath: dualWritePaths.legacy.messages,
                requiresMigration: dualWritePaths.requiresMigration
            });

            diagnostic.collectionPaths = {
                chatType,
                chatId,
                current: dualWritePaths.current,
                legacy: dualWritePaths.legacy,
                requiresMigration: dualWritePaths.requiresMigration
            };

            if (!chatId) {
                diagnostic.issues.push('Chat ID could not be determined');
            }

            if (!dualWritePaths.current.messages) {
                diagnostic.issues.push('Current message path could not be determined');
            }

        } catch (error) {
            diagnostic.issues.push(`Collection path resolution failed: ${error.message}`);
            console.error('❌ Collection path error:', error);
        }
    }

    // Check 4: Firebase Listeners
    const hasLatestMessagesListener = !!state.latestMessagesUnsubscribe;
    const hasMessagesListener = !!state.messagesSnapshotUnsubscribe;

    console.log('🔗 Firebase Listeners:', {
        latestMessages: hasLatestMessagesListener ? '✅ Active' : '❌ Inactive',
        chatMessages: hasMessagesListener ? '✅ Active' : '❌ Inactive'
    });

    if (!hasLatestMessagesListener) {
        diagnostic.warnings.push('Latest messages listener is not active');
    }

    if (!hasMessagesListener) {
        diagnostic.warnings.push('Chat messages listener is not active');
    }

    // Check 5: Message Data
    if (state.latestMessages?.length > 0) {
        const relevantLatestMessage = state.latestMessages.find(msg =>
            chat && (
                msg.id === chat.id ||
                msg.chat_id === chat.id ||
                msg.sender === chat.participants?.data?.[0]?.id ||
                msg.recipient === chat.participants?.data?.[0]?.id
            )
        );

        console.log('📨 Latest Messages:', {
            total: state.latestMessages.length,
            relevantMessage: relevantLatestMessage ? {
                message: relevantLatestMessage.message,
                created_time: relevantLatestMessage.created_time,
                page_id: relevantLatestMessage.page_id
            } : 'None found for current chat'
        });

        if (relevantLatestMessage && !relevantLatestMessage.page_id) {
            diagnostic.warnings.push('Latest message missing page_id - may not appear in chat list');
        }
    }

    if (state.messages?.length > 0) {
        console.log('💬 Chat Messages:', {
            total: state.messages.length,
            latest: state.messages[state.messages.length - 1]?.message || 'N/A'
        });
    } else {
        diagnostic.warnings.push('No messages in current chat');
    }

    // Generate Recommendations
    if (diagnostic.issues.length === 0 && diagnostic.warnings.length === 0) {
        diagnostic.recommendations.push('Message flow appears to be configured correctly');
    } else {
        if (!chat) {
            diagnostic.recommendations.push('Select a chat to enable message functionality');
        }
        if (!selectedPage?.id) {
            diagnostic.recommendations.push('Ensure a page is selected in Redux state');
        }
        if (!hasLatestMessagesListener) {
            diagnostic.recommendations.push('Call fetchLatestMessages to activate listener');
        }
        if (!hasMessagesListener) {
            diagnostic.recommendations.push('Call fetchMessages for the selected chat');
        }
    }

    // Summary
    console.log('\n📊 DIAGNOSTIC SUMMARY:');
    console.log(`Issues: ${diagnostic.issues.length}`);
    console.log(`Warnings: ${diagnostic.warnings.length}`);

    if (diagnostic.issues.length > 0) {
        console.log('\n❌ ISSUES:');
        diagnostic.issues.forEach(issue => console.log(`  • ${issue}`));
    }

    if (diagnostic.warnings.length > 0) {
        console.log('\n⚠️  WARNINGS:');
        diagnostic.warnings.forEach(warning => console.log(`  • ${warning}`));
    }

    if (diagnostic.recommendations.length > 0) {
        console.log('\n💡 RECOMMENDATIONS:');
        diagnostic.recommendations.forEach(rec => console.log(`  • ${rec}`));
    }

    console.groupEnd();

    return diagnostic;
};

/**
 * Test message sending flow
 * @param {Object} store - Redux store
 * @param {string} testMessage - Test message content
 * @returns {Promise<Object>} Test results
 */
export const testMessageSending = async (store, testMessage = 'Test message') => {
    console.group('🧪 TESTING MESSAGE SENDING');

    const state = store.getState().metaBusinessSuite;
    const selectedChat = state.selectedChat;
    const selectedPage = state.selectedPage;

    if (!selectedChat || !selectedPage) {
        console.error('❌ Cannot test - missing selected chat or page');
        console.groupEnd();
        return { success: false, reason: 'Missing selected chat or page' };
    }

    try {
        // Create test FormData
        const formData = new FormData();
        formData.append('message', testMessage);
        formData.append('page_id', selectedPage.id);
        formData.append('access_token', selectedPage.page_token);
        formData.append('id', selectedChat.id);
        formData.append('type', 'text');

        console.log('📤 Sending test message:', {
            message: testMessage,
            chatId: selectedChat.id,
            pageId: selectedPage.id
        });

        // Dispatch sendMessage
        const result = await store.dispatch({
            type: 'metaBusinessSuite/sendMessage',
            payload: {
                data: formData,
                selectedChat
            }
        });

        console.log('📨 Send result:', result);
        console.groupEnd();

        return {
            success: result.type?.includes('fulfilled'),
            result
        };

    } catch (error) {
        console.error('❌ Test failed:', error);
        console.groupEnd();
        return { success: false, error: error.message };
    }
};

/**
 * Test incoming message handling
 * @param {Object} store - Redux store
 * @param {Object} testMessage - Test message object
 * @returns {Promise<Object>} Test results
 */
export const testIncomingMessage = async (store, testMessage = null) => {
    console.group('🧪 TESTING INCOMING MESSAGE');

    const state = store.getState().metaBusinessSuite;
    const selectedChat = state.selectedChat;

    const message = testMessage || {
        id: `test_incoming_${Date.now()}`,
        message: 'Test incoming message',
        sender: selectedChat?.participants?.data?.[0]?.id || 'test_sender',
        recipient: selectedChat?.participants?.data?.[1]?.id || 'test_recipient',
        created_time: new Date().toISOString(),
        type: 'text'
    };

    console.log('📥 Test incom:', message);

    try {
        // Import and use the incoming message handler
        const { handleIncomingMessages } = await import('./incomingMessageHandler');

        const result = await handleIncomingMessages(store.dispatch, [message], selectedChat);

        console.log('📨 Incoming message result:', result);
        console.groupEnd();

        return result;

    } catch (error) {
        console.error('❌ Test failed:', error);
        console.groupEnd();
        return { success: false, error: error.message };
    }
};

// Browser console helpers
if (typeof window !== 'undefined') {
    window.diagnoseMessageFlow = (store) => diagnoseMessageFlow(store);
    window.testMessageSending = (store, message) => testMessageSending(store, message);
    window.testIncomingMessage = (store, message) => testIncomingMessage(store, message);

    window.quickDiagnose = (store) => {
        console.log('🚀 QUICK MESSAGE FLOW DIAGNOSIS');
        const diagnostic = diagnoseMessageFlow(store);

        if (diagnostic.issues.length === 0) {
            console.log('✅ No critical issues found');
            console.log('💡 Try: window.testMessageSending(store) or window.testIncomingMessage(store)');
        } else {
            console.log('❌ Issues found - check diagnostic results above');
        }

        return diagnostic;
    };
}

export default {
    diagnoseMessageFlow,
    testMessageSending,
    testIncomingMessage
};
