import React, { useState, useRef, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { FaPaperPlane, FaImage } from "react-icons/fa6";
import { FaTimes } from "react-icons/fa";
import { BsEmojiSmile } from "react-icons/bs";
import { Spinner } from "react-bootstrap";
import EmojiPicker from "emoji-picker-react";
import {
  selectSelectedChat,
  selectSelectedWhatsappChat,
  selectActiveFilter,
  sendCommentHybrid,
} from "../../redux/features/metaBusinessChatSlice";
import { getChatIds } from "../../services/comments/hybridService";
import { validateCommentText } from "../../utils/commentValidation";
import {
  handleCommentError,
  networkMonitor,
} from "../../utils/commentErrorHandling";
import "./CommentInput.css";

const CommentInput = ({
  onSendComment,
  disabled = false,
  placeholder = "Add a comment...",
}) => {
  const dispatch = useDispatch();
  const selectedChat = useSelector(selectSelectedChat);
  const selectedWhatsappChat = useSelector(selectSelectedWhatsappChat);
  const activeFilter = useSelector(selectActiveFilter);
  const currentUser = useSelector((state) => state.auth.user);

  const [commentText, setCommentText] = useState("");
  const [sendingComment, setSendingComment] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [textareaHeight, setTextareaHeight] = useState("auto");
  const [validationError, setValidationError] = useState("");
  const [isOnline, setIsOnline] = useState(networkMonitor.checkConnection());
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  const textareaRef = useRef(null);
  const emojiButtonRef = useRef(null);
  const emojiPickerRef = useRef(null);
  const fileInputRef = useRef(null);

  // Character limit
  const CHARACTER_LIMIT = 1000;

  // Monitor network connectivity
  useEffect(() => {
    const unsubscribe = networkMonitor.addListener((status, online) => {
      setIsOnline(online);
    });

    return unsubscribe;
  }, []);

  // All authenticated users can write comments - no need for permission validation
  useEffect(() => {
    if (currentUser) {
      setValidationError("");
    }
  }, [currentUser]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      const scrollHeight = textareaRef.current.scrollHeight;
      const maxHeight = 120; // Maximum height in pixels
      const newHeight = Math.min(scrollHeight, maxHeight);
      textareaRef.current.style.height = `${newHeight}px`;
      setTextareaHeight(`${newHeight}px`);
    }
  }, [commentText]);

  // Handle emoji click
  const onEmojiClick = (emojiObject) => {
    setCommentText((prev) => prev + emojiObject.emoji);
    setShowEmojiPicker(false);
    // Focus back to textarea
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  // Handle image selection
  const handleImageSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        setValidationError("Please select a valid image file.");
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        setValidationError("Image size must be less than 5MB.");
        return;
      }

      setSelectedImage(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);

      setValidationError("");
    }
  };

  // Remove selected image
  const removeImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(event.target) &&
        !emojiButtonRef.current?.contains(event.target)
      ) {
        setShowEmojiPicker(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Get chat IDs for hybrid operations
  const getChatInfo = () => {
    return getChatIds(selectedChat, selectedWhatsappChat, activeFilter);
  };

  // Handle send comment with comprehensive validation and error handling
  const handleSendComment = async (e) => {
    e.preventDefault();

    const trimmedText = commentText.trim();
    if ((!trimmedText && !selectedImage) || sendingComment || disabled) return;

    // Clear previous validation errors
    setValidationError("");

    // Validate comment text
    const textValidation = validateCommentText(trimmedText);
    if (!textValidation.isValid) {
      setValidationError(textValidation.errors.join(", "));
      return;
    }

    // Check network connectivity
    if (!isOnline) {
      setValidationError(
        "No internet connection. Please check your connection and try again."
      );
      return;
    }

    const chatInfo = getChatInfo();
    if (!chatInfo) {
      setValidationError("No chat selected. Please select a chat first.");
      return;
    }

    if (!currentUser?.user) {
      setValidationError("You must be logged in to send comments.");
      return;
    }

    // All authenticated users can write comments - no permission check needed

    setSendingComment(true);

    try {
      const author = {
        id: String(currentUser.user.id), // Convert to string since validation expects string
        name: currentUser.user.name || currentUser.user.email,
        email: currentUser.user.email,
      };

      // Send comment using hybrid service (backend + Firebase)
      const result = await dispatch(
        sendCommentHybrid({
          backendChatId: chatInfo.backendChatId,
          firebaseChatId: chatInfo.firebaseChatId,
          chatType: chatInfo.chatType,
          text: trimmedText,
          image: selectedImage, // Include image if selected
          author: author,
        })
      ).unwrap();

      // Clear input and validation errors on success
      setCommentText("");
      setValidationError("");
      removeImage(); // Clear selected image

      // Call parent callback if provided
      if (onSendComment) {
        onSendComment(result);
      }
    } catch (error) {
      // Use centralized error handling
      const commentError = handleCommentError(error, "send comment", {
        showToast: false, // Don't show toast, show in component
        throwError: false,
      });

      setValidationError(commentError.message);
    } finally {
      setSendingComment(false);
    }
  };

  // Handle textarea key press
  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendComment(e);
    }
  };

  // Check if send button should be enabled
  const canSend =
    (commentText.trim() || selectedImage) &&
    !sendingComment &&
    !disabled &&
    !validationError &&
    isOnline;

  // Get remaining characters
  const remainingChars = CHARACTER_LIMIT - commentText.length;
  const isNearLimit = remainingChars <= 100;
  const isOverLimit = remainingChars < 0;

  return (
    <form className="comment-input-form" onSubmit={handleSendComment}>
      {/* Image Preview */}
      {imagePreview && (
        <div className="image-preview-container">
          <div className="image-preview">
            <img src={imagePreview} alt="Preview" className="preview-image" />
            <button
              type="button"
              className="remove-image-btn"
              onClick={removeImage}
              title="Remove image"
            >
              <FaTimes />
            </button>
          </div>
        </div>
      )}

      <div className="comment-input-wrapper">
        <textarea
          ref={textareaRef}
          className={`comment-textarea ${isOverLimit ? "over-limit" : ""}`}
          placeholder={selectedImage ? "Add a caption..." : placeholder}
          value={commentText}
          onChange={(e) => setCommentText(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={sendingComment || disabled}
          rows={1}
          style={{ height: textareaHeight }}
        />

        <div className="comment-input-actions">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleImageSelect}
            accept="image/*"
            style={{ display: "none" }}
          />

          <button
            type="button"
            className="image-btn"
            onClick={() => fileInputRef.current?.click()}
            disabled={sendingComment || disabled}
            title="Add image"
          >
            <FaImage />
          </button>

          <button
            type="button"
            className="emoji-btn"
            onClick={() => {
              setShowEmojiPicker(!showEmojiPicker);
            }}
            disabled={sendingComment || disabled}
            ref={emojiButtonRef}
            title="Add emoji"
          >
            <BsEmojiSmile />
          </button>

          <button
            type="submit"
            className="send-comment-btn"
            disabled={!canSend}
            title="Send comment"
          >
            {sendingComment ? (
              <Spinner animation="border" size="sm" variant="primary" />
            ) : (
              <FaPaperPlane />
            )}
          </button>
        </div>
      </div>

      {/* Validation error */}
      {validationError && (
        <div className="comment-validation-error">{validationError}</div>
      )}

      {/* Network status */}
      {!isOnline && (
        <div className="comment-network-status offline">
          No internet connection. Comments will be queued when connection is
          restored.
        </div>
      )}

      {/* Character count */}
      {(isNearLimit || isOverLimit) && (
        <div
          className={`character-count ${
            isOverLimit ? "over-limit" : "near-limit"
          }`}
        >
          {remainingChars} characters remaining
        </div>
      )}

      {/* Emoji picker */}
      {showEmojiPicker && (
        <div className="comment-emoji-picker-container" ref={emojiPickerRef}>
          <EmojiPicker
            onEmojiClick={onEmojiClick}
            searchDisabled={false}
            skinTonesDisabled={false}
            width={300}
            height={350}
            previewConfig={{
              showPreview: true,
            }}
          />
        </div>
      )}
    </form>
  );
};

export default CommentInput;
