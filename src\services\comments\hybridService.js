/**
 * Hybrid Comments Service
 * Combines backend API (for persistence/history) with Firebase (for real-time)
 */

import {
    getCommentsFromBackend,
    sendCommentToBackend,
    markCommentsAsReadInBackend
} from './backendApi';

import {
    addComment as addCommentToFirebase,
    listenToComments as listenToFirebaseComments
} from './index';

// Firebase imports removed as we no longer clear Firebase comments
// Backend is now the primary source of truth
import { db } from '../../utils/firebase.config';

// Removed clearFirebaseComments function as it was causing comments to disappear
// for other users when someone opened the modal. Backend is now the primary source.

// Removed syncReadReceiptsToFirebase function as it was causing Firebase errors
// Backend is the primary source for read receipts, Firebase only handles new real-time comments

/**
 * Load comments for a chat (hybrid approach)
 * 1. Clear Firebase comments
 * 2. Load history from backend
 * 3. Set up Firebase listener for new real-time comments
 * @param {string} backendChatId - The chat ID from backend/chats endpoint
 * @param {string} firebaseChatId - The Firebase chat identifier
 * @param {string} chatType - The chat type ('whatsapp', 'messenger', 'instagram')
 * @param {Function} onCommentsUpdate - Callback for comment updates
 * @returns {Promise<{comments: Array, unsubscribe: Function}>}
 */
export const loadChatComments = async (backendChatId, firebaseChatId, chatType, onCommentsUpdate, pageId = null) => {
    try {
        // Step 1: Load comment history from backend (primary source)
        console.log('Loading comments for backendChatId:', backendChatId);
        const backendComments = await getCommentsFromBackend(backendChatId);
        console.log('Backend comments loaded:', backendComments);

        // Step 2: Set up Firebase listener for new real-time comments and read receipt updates
        const unsubscribe = listenToFirebaseComments(firebaseChatId, chatType, (firebaseComments) => {
            console.log('Firebase comments received:', firebaseComments);

            // Create a map of Firebase comments by ID for quick lookup
            const firebaseCommentsMap = new Map();
            firebaseComments.forEach(fc => {
                firebaseCommentsMap.set(String(fc.id), fc);
            });

            // Merge backend comments with Firebase read receipts and new comments
            const mergedComments = backendComments.map(backendComment => {
                const firebaseComment = firebaseCommentsMap.get(String(backendComment.id));

                if (firebaseComment && firebaseComment.readBy) {
                    // Merge read receipts from Firebase (real-time) with backend comment
                    return {
                        ...backendComment,
                        readBy: firebaseComment.readBy || backendComment.readBy
                    };
                }

                return backendComment;
            });

            // Add new Firebase comments that don't exist in backend
            const backendCommentIds = new Set(backendComments.map(c => String(c.id)));
            const newFirebaseComments = firebaseComments.filter(fc =>
                !backendCommentIds.has(String(fc.id)) && fc.chatType !== 'backend'
            );

            // Combine merged backend comments with new Firebase comments
            const allComments = [...mergedComments, ...newFirebaseComments];

            // Sort by creation time
            allComments.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

            console.log('All comments merged and sorted:', allComments);
            onCommentsUpdate(allComments);
        }, null, pageId);

        // Note: We don't sync read receipts to Firebase since backend is the primary source
        // Firebase is only used for new real-time comments, not for storing backend comment data

        // Initial callback with backend comments
        console.log('Initial callback with backend comments:', backendComments);
        onCommentsUpdate(backendComments);

        return {
            comments: backendComments,
            unsubscribe
        };
    } catch (error) {
        console.error('Error loading chat comments:', error);

        // Fallback: just set up Firebase listener
        const unsubscribe = listenToFirebaseComments(firebaseChatId, chatType, onCommentsUpdate, null, pageId);
        onCommentsUpdate([]);

        return {
            comments: [],
            unsubscribe
        };
    }
};

/**
 * Send a new comment (hybrid approach)
 * 1. Send to backend for persistence
 * 2. Send to Firebase for real-time updates
 * @param {string} backendChatId - The chat ID from backend/chats endpoint
 * @param {string} firebaseChatId - The Firebase chat identifier
 * @param {string} chatType - The chat type ('whatsapp', 'messenger', 'instagram')
 * @param {string} text - The comment text
 * @param {Object} author - The author information
 * @param {string} pageId - The page identifier for organizing chats by page
 * @returns {Promise<Object>} - The created comment
 */
export const sendComment = async (backendChatId, firebaseChatId, chatType, text, author, pageId = null) => {
    try {
        // Send to backend first (primary storage)
        const backendComment = await sendCommentToBackend(backendChatId, text, author);

        // Send to Firebase for real-time updates using the same ID as backend
        try {
            const { addComment: addCommentToFirebase } = await import('./index');

            // Create Firebase comment with same ID as backend comment
            const firebaseComment = {
                id: String(backendComment.id),
                text: text,
                author: author,
                createdAt: backendComment.createdAt,
                readBy: backendComment.readBy || [],
                chatType: 'backend' // Mark as backend-originated
            };

            await addCommentToFirebase(firebaseChatId, chatType, text, author, null, String(backendComment.id), pageId);
        } catch (firebaseError) {
            console.warn('Failed to send comment to Firebase (real-time may not work):', firebaseError);
            // Don't throw - backend success is more important
        }

        return backendComment;
    } catch (error) {
        console.error('Error sending comment:', error);
        throw error;
    }
};

/**
 * Mark comments as read (hybrid approach)
 * 1. Mark as read in backend
 * 2. Mark as read in Firebase (if comments exist there)
 * @param {string} backendChatId - The chat ID from backend/chats endpoint
 * @param {string} firebaseChatId - The Firebase chat identifier
 * @param {string} chatType - The chat type ('whatsapp', 'messenger', 'instagram')
 * @param {Array} commentIds - Array of comment IDs (backend IDs)
 * @param {Object} user - User information
 * @returns {Promise<void>}
 */
export const markCommentsAsRead = async (backendChatId, firebaseChatId, chatType, commentIds, user, pageId = null) => {
    try {
        // Mark as read in backend (primary)
        await markCommentsAsReadInBackend(backendChatId, commentIds, user);

        // Also update Firebase for real-time synchronization
        // This ensures other users see updated read receipts immediately
        try {
            const { markCommentsAsRead: markCommentsAsReadInFirebase } = await import('./index');
            await markCommentsAsReadInFirebase(firebaseChatId, chatType, commentIds, user, null, pageId);
            console.log(`Updated read receipts in Firebase for real-time sync`);
        } catch (firebaseError) {
            console.warn('Failed to update read receipts in Firebase (real-time sync may not work):', firebaseError);
            // Don't throw - backend success is more important
        }

        console.log(`Marked ${commentIds.length} comments as read for chat ${backendChatId}`);
    } catch (error) {
        console.error('Error marking comments as read:', error);
        throw error;
    }
};

/**
 * Get chat IDs for hybrid operations using consistent chat ID resolution
 *
 * This function ensures that both the backend API and Firebase use consistent
 * chat identifiers, enabling proper synchronization between the two systems.
 *
 * The standardized approach ensures:
 * - WhatsApp: Uses normalized phone number for both backend and Firebase
 * - Messenger/Instagram: Uses backend chat ID for both systems (not participant ID)
 * - Comments and messages use identical identifiers for data consistency
 *
 * @param {Object} selectedChat - The selected chat object (Messenger/Instagram)
 * @param {Object} selectedWhatsappChat - The selected WhatsApp chat object
 * @param {string} activeFilter - The active filter ('whatsapp' or other)
 * @returns {Object|null} - Object with backendChatId and firebaseChatId
 */
export const getChatIds = (selectedChat, selectedWhatsappChat, activeFilter) => {
    // Import the collection paths service for consistent chat ID resolution
    const { getChatIdentifier, determineChatType } = require('../../services/firebase/collectionPaths');

    if (activeFilter === "whatsapp" && selectedWhatsappChat) {
        const chatType = 'whatsapp';
        const firebaseChatId = getChatIdentifier(selectedWhatsappChat, chatType); // Normalized phone number

        return {
            backendChatId: selectedWhatsappChat.id || selectedWhatsappChat.sender_phone_number, // Backend chat ID
            firebaseChatId: firebaseChatId, // Consistent Firebase chat ID (normalized phone number)
            chatType: chatType
        };
    } else if (selectedChat) {
        const chatType = determineChatType(selectedChat);
        const firebaseChatId = getChatIdentifier(selectedChat, chatType); // Backend chat ID (not participant ID)

        return {
            backendChatId: selectedChat.id, // Backend chat ID
            firebaseChatId: firebaseChatId, // Consistent Firebase chat ID (backend chat ID, ensuring messages and comments align)
            chatType: chatType
        };
    }
    return null;
};
