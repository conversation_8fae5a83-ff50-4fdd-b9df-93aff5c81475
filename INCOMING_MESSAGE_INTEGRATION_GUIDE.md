# Incoming Message Integration Guide

## Overview
This guide explains how to integrate incoming messages (from webhooks) with the new Firebase collection consistency implementation.

## How It Works

### 1. **Incoming Message Flow**
```
Webhook/API → handleIncomingMessage → updateFirebaseParentDocument → Firebase Listeners → UI Update
```

### 2. **Required Integration Points**

#### **A. Webhook Handler Integration**
Your webhook handler should call the `handleIncomingMessage` function:

```javascript
import { handleIncomingMessages } from '../utils/incomingMessageHandler';

// In your webhook handler
app.post('/webhook/messenger', async (req, res) => {
  try {
    const incomingMessages = parseWebhookData(req.body); // Your parsing logic

    // Process each message
    for (const message of incomingMessages) {
      await handleIncomingMessages(dispatch, [message], selectedChat);
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).send('Error');
  }
});
```

#### **B. Message Format**
Incoming messages should follow this format:

```javascript
const incomingMessage = {
  id: 'message_id_from_api',           // Required: Unique message ID
  message: 'Hello from customer',      // Required: Message content
  sender: 'customer_id',               // Required: Customer/sender ID
  recipient: 'page_id_or_business_id', // Required: Your business ID
  created_time: '2024-01-01T10:00:00Z', // Required: ISO timestamp
  type: 'text',                        // Optional: message type (text, image, etc.)
  from: {                              // Optional: Alternative sender format
    id: 'customer_id'
  }
};
```

### 3. **Firebase Structure After Integration**

#### **Parent Document** (for latest messages list)
```
chats/{chatId}/
  - sender: "customer_id"
  - recipient: "business_id"
  - message: "latest message content"
  - created_time: "2024-01-01T10:00:00Z"
  - updated_time: "2024-01-01T10:00:00Z"
  - type: "text"
  - page_id: "your_page_id"     // ✅ Critical for latest messages listener
  - chat_id: "chat_id"
  - chat_type: "messenger"
```

#### **Message Document** (for individual messages)
```
chats/{chatId}/messages/{messageId}/
  - id: "message_id"
  - message: "message content"
  - sender: "customer_id"
  - recipient: "business_id"
  - created_time: "2024-01-01T10:00:00Z"
  - type: "text"
```

## Testing Integration

### 1. **Browser Console Testing**
```javascript
// Test incoming message handling
const testMessage = {
  id: 'test_incoming_123',
  message: 'Test incoming message',
  sender: 'test_customer_id',
  recipient: 'your_business_id',
  created_time: new Date().toISOString(),
  type: 'text'
};

// Test with current selected chat
window.testIncomingMessage(dispatch, testMessage);

// Or test with specific chat
window.testIncomingMessage(dispatch, testMessage, selectedChat);
```

### 2. **Debug Logs to Watch**
```
[INCOMING_MESSAGE_HANDLER] Processing incoming messages: {...}
[INCOMING_MESSAGE] Updating Firebase parent document: {...}
[INCOMING_MESSAGE] Firebase parent document and message updated successfully
[QUERY_PERFORMANCE] fetchLatestMessages: {...}
[DEBUG] updateMessages called with (new path): [...]
```

### 3. **Verification Steps**
1. **Send test message** → Check console logs
2. **Check Firebase console** → Verify parent document has `page_id`
3. **Check latest messages list** → Should update immediately
4. **Check chat messages** → Should appear in conversation

## Common Issues & Solutions

### Issue 1: Latest Messages Not Updating
**Symptoms**: Incoming messages don't appear in chat list
**Solution**: Ensure `page_id` is included in parent document

```javascript
// ✅ Correct - includes page_id
const docData = {
  // ... other fields
  page_id: selectedPage?.id, // This is critical!
  chat_id: chatId,
  chat_type: chatType
};
```

### Issue 2: Messages Not Appearing in Chat
**Symptoms**: Latest messages update but individual messages don't show
**Solution**: Ensure messages are written to subcollection

```javascript
// ✅ Write to both parent document AND subcollection
await setDoc(chatDocRef, docData, { merge: true });           // Parent
await setDoc(messageRef, messageData);                        // Subcollection
```

### Issue 3: Chat ID Mismatch
**Symptoms**: Messages appear in wrong chat or not at all
**Solution**: Use consistent chat ID resolution

```javascript
// ✅ Use the same logic as outgoing messages
const chatType = determineChatType(selectedChat);
const dualWritePaths = getDualWritePaths(selectedChat, chatType);
const chatId = dualWritePaths.chatId;
```

## Integration Checklist

- [ ] Webhook handler calls `handleIncomingMessages`
- [ ] Message format includes all required fields
- [ ] `selectedChat` object is available or passed to handler
- [ ] `page_id` is correctly set in Redux state
- [ ] Firebase security rules allow writes to chat collections
- [ ] Real-time listeners are active for the chat
- [ ] Console logs show successful processing
- [ ] Firebase console shows correct document structure
- [ ] UI updates with new messages

## Example Webhook Integration

```javascript
// Example Express.js webhook handler
import { store } from '../redux/store';
import { handleIncomingMessages } from '../utils/incomingMessageHandler';

app.post('/webhook/messenger', async (req, res) => {
  try {
    const entry = req.body.entry?.[0];
    const messaging = entry?.messaging?.[0];

    if (messaging?.message) {
      const incomingMessage = {
        id: messaging.message.mid,
        message: messaging.message.text,
        sender: messaging.sender.id,
        recipient: messaging.recipient.id,
        created_time: new Date(messaging.timestamp).toISOString(),
        type: 'text'
      };

      // Get current selected chat from Redux state
      const state = store.getState();
      const selectedChat = state.metaBusinessSuite.selectedChat;

      // Handle the incoming message
      await handleIncomingMessages(
        store.dispatch,
        [incomingMessage],
        selectedChat
      );

      console.log('✅ Incoming message processed successfully');
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('❌ Webhook error:', error);
    res.status(500).send('Error processing webhook');
  }
});
```

## Next Steps

1. **Integrate with your webhook handler** using the examples above
2. **Test with real incoming messages** from Messenger
3. **Monitor console logs** to ensure proper processing
4. **Verify Firebase structure** matches the expected format
5. **Test real-time updates** in the UI

The incoming message integration should now work correctly with the Firebase collection consistency implementation!
