import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import {
  collection,
  deleteDoc,
  doc,
  getDocs,
  onSnapshot,
  orderBy,
  query,
  setDoc,
  where,
  collectionGroup,
} from "firebase/firestore";
import metaService from "../../services/integrations/meta";
import { db } from "../../utils/firebase.config";
import { BigIntUtil } from "../../utils/BigIntUtil";
import { createSelector } from 'reselect';
import { getDualWritePaths, determineChatType, getChatIdentifier, getCollectionPaths, getSenderId } from "../../services/firebase/collectionPaths";
import {
  listenToMessagesOptimized,
  listenToWhatsAppMessagesOptimized,
  fetchOptimizedMessages,
  getQueryPerformanceStats
} from "../../services/firebase/queryOptimization";

/**
 * Logging utility for dual write operations during migration phase
 *
 * This utility tracks the success/failure of dual write operations where messages
 * are written to both legacy (sender ID) and new (chat ID) collection paths.
 *
 * @param {string} operation - The operation being performed (e.g., 'sendMessage', 'updateMessage')
 * @param {boolean} success - Whether the operation was successful
 * @param {Error|null} error - Error object if operation failed
 * @param {Object} metadata - Additional metadata for logging
 */
const logDualWriteOperation = (operation, success, error = null, metadata = {}) => {
  const logData = {
    timestamp: new Date().toISOString(),
    operation,
    success,
    error: error ? error.message : null,
    ...metadata
  };

  if (success) {
    console.log(`[DUAL_WRITE_SUCCESS] ${operation}:`, logData);
  } else {
    console.error(`[DUAL_WRITE_FAILURE] ${operation}:`, logData);
  }

  // In a production environment, you might want to send this to an analytics service
  // Example: analytics.track('dual_write_operation', logData);
};

/**
 * Utility function to normalize phone numbers consistently across the application
 *
 * This ensures WhatsApp phone numbers are formatted consistently for use as
 * Firebase collection document identifiers. The normalization removes the leading
 * '+' sign and any spaces to create a clean identifier.
 *
 * @param {string|number} phoneNumber - The phone number to normalize
 * @returns {string} - Normalized phone number without + prefix and spaces
 */
const normalizePhoneNumber = (phoneNumber) => {
  if (!phoneNumber) return '';
  return phoneNumber.toString().trim().replace(/^\+|\s+/g, '');
};

/**
 * Fallback function for latest messages when collectionGroup index is missing
 * Uses the legacy chats collection approach temporarily
 */
const setupLegacyLatestMessagesListener = (dispatch, selectedPage) => {
  console.log('[FALLBACK] Setting up legacy latest messages listener for page:', selectedPage.id);

  try {
    const fallbackQuery = query(
      collection(db, "chats"),
      orderBy("created_time", "desc")
    );

    const fallbackUnsubscribe = onSnapshot(fallbackQuery, (snapshot) => {
      const startTime = performance.now();

      const updatedMessages = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      const processingTime = Math.round((performance.now() - startTime) * 100) / 100;
      console.log('[QUERY_PERFORMANCE] fetchLatestMessages (legacy fallback):', {
        messagesCount: updatedMessages.length,
        processingTime
      });

      dispatch(setLatestMessages(updatedMessages));
    }, (error) => {
      console.error('[QUERY_ERROR] Legacy fallback listener error:', error);
      dispatch(setLatestMessages([]));
    });

    dispatch(setLatestMessagesUnsubscribe(fallbackUnsubscribe));
    return fallbackUnsubscribe;

  } catch (error) {
    console.error('[FALLBACK_ERROR] Error setting up legacy listener:', error);
    dispatch(setLatestMessages([]));
    return null;
  }
};

// Helper function for consistent date parsing
const parseDate = (dateString) => {
  if (!dateString) return new Date(0);

  // Handle different date formats
  if (typeof dateString === 'string') {
    // If it contains a space but no 'T', replace space with 'T'
    if (dateString.includes(' ') && !dateString.includes('T')) {
      dateString = dateString.replace(' ', 'T');
    }
  }

  const date = new Date(dateString);
  return isNaN(date.getTime()) ? new Date(0) : date;
};

/**
 * Helper function to extract the latest message for a specific chat ID
 *
 * This function searches through the latest messages array to find messages
 * associated with a specific chat ID. It checks both sender and recipient fields
 * to handle bidirectional conversations and returns the most recent message.
 *
 * Note: This function maintains compatibility with the existing Context API
 * implementation while supporting the new chat ID-based collection structure.
 *
 * @param {string} chatId - The chat identifier to search for
 * @param {Array} latestMessages - Array of latest messages from Firebase
 * @returns {Object|null} - The latest message object or null if not found
 */
const extractLatestMessage = (chatId, latestMessages) => {
  console.log('extractLatestMessage called with:', { chatId, latestMessagesCount: latestMessages?.length });

  if (!latestMessages || !Array.isArray(latestMessages)) {
    console.log('No latestMessages or not array:', latestMessages);
    return null;
  }

  // Search for messages using multiple identification methods for compatibility
  // 1. Check document ID (new chat ID-based approach)
  // 2. Check chat_id field (explicit chat ID field)
  // 3. Check sender/recipient (legacy approach)
  const latestMessage = latestMessages.find(
    (chat) =>
      chat?.id?.toString() === chatId?.toString() ||           // Document ID matches chat ID
      chat?.chat_id?.toString() === chatId?.toString() ||      // Explicit chat_id field
      chat?.sender?.toString() === chatId?.toString() ||       // Legacy: sender matches
      chat?.recipient?.toString() === chatId?.toString()       // Legacy: recipient matches
  );

  console.log('Found message for chatId', chatId, ':', {
    found: !!latestMessage,
    message: latestMessage?.message,
    matchedBy: latestMessage?.id === chatId ? 'document_id' :
      latestMessage?.chat_id === chatId ? 'chat_id_field' :
        latestMessage?.sender === chatId ? 'sender_field' :
          latestMessage?.recipient === chatId ? 'recipient_field' : 'unknown',
    sampleLatestMessage: latestMessages[0]
  });

  const result = latestMessage ? {
    ...latestMessage,
    message: latestMessage?.message,
    message_id: latestMessage?.message_id,
    sender_name: latestMessage?.sender_name,
    created_time: latestMessage?.created_time || latestMessage?.updated_time,
  } : null;

  console.log('extractLatestMessage result:', result);
  return result;
};

// Helper function to format timestamp (same as Context API)
const formatTimestamp = (timestamp, currentDate) => {
  if (!timestamp) return "";

  const messageDate = new Date(timestamp);
  const messageDay = messageDate.toDateString();
  const currentDay = currentDate.toDateString();

  if (messageDay === currentDay) {
    return messageDate.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  } else {
    const yesterday = new Date(currentDate);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayDay = yesterday.toDateString();

    if (messageDay === yesterdayDay) {
      return "Yesterday";
    } else {
      return messageDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
    }
  }
};

const parsePhoneNumber = (phoneNumber) => {
  const numericPhoneNumber = phoneNumber.replace(/\D/g, "");
  return "+" + numericPhoneNumber;
};

// Async thunks for API calls
export const fetchLatestMessages = createAsyncThunk(
  "metaBusinessSuite/fetchLatestMessages",
  async (_, { rejectWithValue, dispatch, getState }) => {
    try {
      const state = getState().metaBusinessSuite;
      const selectedPage = state.selectedPage;

      if (!selectedPage?.id) {
        return { unsubscribe: null };
      }

      // Clean up any existing listener first to avoid multiple open channels
      const prevUnsub = state.latestMessagesUnsubscribe;
      if (prevUnsub) {
        try {
          prevUnsub();
        } catch (e) {
          console.warn("failed to unsubscribe previous listener", e);
        }
      }

      // Try to use collectionGroup first, but fallback if index is missing
      let unsubscribe = null;

      try {
        // Listen to the specific page's collection dynamically
        // Structure: ${pageId}/${sender}/messages
        // Use collectionGroup to get all messages subcollections under the page
        const messagesQuery = query(
          collectionGroup(db, "messages"),
          orderBy("created_time", "desc")
        );

        console.log('[QUERY_OPTIMIZATION] Setting up dynamic page-based listener for page:', selectedPage.id);

        // Set up listener that filters messages for the current page dynamically
        unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
          const startTime = performance.now();

          // Group messages by sender and get the latest message from each
          const messagesBySender = new Map();

          snapshot.docs.forEach((doc) => {
            const data = doc.data();
            const docPath = doc.ref.path; // e.g., "pageId/senderId/messages/messageId"
            const pathParts = docPath.split('/');

            // Filter for current page: ${pageId}/${sender}/messages/messageId
            if (pathParts.length >= 4 && pathParts[0] === selectedPage.id.toString()) {
              const senderId = pathParts[1];
              const messageData = {
                id: doc.id,
                senderId,
                sender: senderId, // For compatibility with existing code
                ...data,
              };

              // Keep only the latest message per sender
              if (!messagesBySender.has(senderId) ||
                new Date(messageData.created_time) > new Date(messagesBySender.get(senderId).created_time)) {
                messagesBySender.set(senderId, messageData);
              }
            }
          });

          const updatedMessages = Array.from(messagesBySender.values());

          const processingTime = Math.round((performance.now() - startTime) * 100) / 100;
          console.log('[QUERY_PERFORMANCE] fetchLatestMessages (dynamic page-based):', {
            messagesCount: updatedMessages.length,
            processingTime,
            pageId: selectedPage.id,
            uniqueSenders: messagesBySender.size
          });

          dispatch(setLatestMessages(updatedMessages));
        }, (error) => {
          console.error('[QUERY_ERROR] Dynamic page-based latest messages listener error:', error);

          // If it's an index error, provide helpful guidance
          if (error.message.includes('index')) {
            console.warn('[FIREBASE_INDEX] CollectionGroup query requires Firebase index. Using fallback approach.');
            console.warn('[FIREBASE_INDEX] To fix this permanently, create the index at:', error.message.match(/https:\/\/[^\s]+/)?.[0]);

            // Fallback: Use legacy approach temporarily
            setupLegacyLatestMessagesListener(dispatch, selectedPage);
          } else {
            dispatch(setLatestMessages([])); // Clear messages on other errors
          }
        });

      } catch (error) {
        console.error('[QUERY_ERROR] Error setting up collectionGroup listener:', error);
        // Fallback to legacy approach
        setupLegacyLatestMessagesListener(dispatch, selectedPage);
      }

      // Save listener reference in state
      dispatch(setLatestMessagesUnsubscribe(unsubscribe));

      return { unsubscribe };
    } catch (error) {
      console.error("Error setting up latest messages listener:", error);
      return rejectWithValue(error.message);
    }
  }
);

export const fetchMessages = createAsyncThunk(
  "metaBusinessSuite/fetchMessages",
  async ({ thread }, { rejectWithValue, getState, dispatch }) => {
    if (!thread) {
      return { messages: [] };
    }
    const state = getState().metaBusinessSuite;
    try {
      const result = await metaService.getMessagesForChatApi({
        ...state.selectedPage,
        access_token: state?.selectedPage?.page_token,
        user_id: thread.id,
      });

      result.sort(
        (a, b) => parseDate(a.created_time) - parseDate(b.created_time),
      );

      // Determine chat type and get simplified collection paths
      const chatType = determineChatType(thread);

      if (!state?.selectedPage?.id) {
        console.error('fetchMessages: No page selected, cannot determine collection paths');
        return { messages: [] };
      }

      // Use simplified path structure: ${pageId}/${sender}/messages
      const collectionPaths = getCollectionPaths(thread, chatType, state.selectedPage.id);

      console.log('fetchMessages - Using simplified collection paths:', {
        chatType,
        messagesPath: collectionPaths.messages,
        commentsPath: collectionPaths.comments,
        threadId: thread.id,
        pageId: state.selectedPage.id
      });

      // Get sender ID for consistent identification
      const senderId = getSenderId(thread, chatType);

      console.log('fetchMessages - Using sender ID:', {
        senderId,
        threadId: thread.id,
        chatType
      });

      const messageFromFirestore = state?.latestMessages
        ? extractLatestMessage(senderId, state?.latestMessages)
        : null;

      let firestoreMessages = [];
      let activeCollectionPath = null;
      let unsubscribeNested = null;

      if (messageFromFirestore && messageFromFirestore.id) {
        // Use simplified path structure: ${pageId}/${sender}/messages
        if (collectionPaths.messages) {
          const startTime = performance.now();

          try {
            console.log(`[QUERY_OPTIMIZATION] fetchMessages - Using simplified path:`, collectionPaths.messages);

            const nestedCollectionRef = collection(db, ...collectionPaths.messages.split('/'));
            const nestedQuery = query(
              nestedCollectionRef,
              orderBy("created_time", "asc"),
            );

            const firestoreSnapshot = await getDocs(nestedQuery);
            firestoreMessages = firestoreSnapshot.docs.map((doc) => {
              const data = doc.data();
              return {
                id: doc.id,
                created_time: data?.created_time,
                from: {
                  id: new BigIntUtil(data?.sender)?.value,
                  email: new BigIntUtil(data?.sender)?.value,
                },
                message: data?.message,
                type: data?.type,
              };
            });

            activeCollectionPath = collectionPaths.messages;

            const processingTime = Math.round((performance.now() - startTime) * 100) / 100;
            console.log(`[QUERY_PERFORMANCE] fetchMessages (simplified path):`, {
              messagesCount: firestoreMessages.length,
              processingTime,
              path: collectionPaths.messages
            });

            // Set up real-time listener for the simplified path
            unsubscribeNested = onSnapshot(nestedQuery, (snapshot) => {
              const startTime = performance.now();

              const liveMessages = snapshot.docs.map((doc) => {
                const data = doc.data();
                return {
                  id: doc.id,
                  created_time: data?.created_time,
                  from: {
                    id: new BigIntUtil(data?.sender)?.value,
                    email: new BigIntUtil(data?.sender)?.value,
                  },
                  message: data?.message,
                  type: data?.type,
                };
              });

              const processingTime = Math.round((performance.now() - startTime) * 100) / 100;
              console.log(`[QUERY_PERFORMANCE] fetchMessages real-time (simplified path):`, {
                messagesCount: liveMessages.length,
                changes: snapshot.docChanges().length,
                processingTime,
                path: collectionPaths.messages
              });

              dispatch(updateMessages(liveMessages));
            }, (error) => {
              console.error('[QUERY_ERROR] fetchMessages - Firebase listener error for simplified path:', error);
            });

          } catch (error) {
            const processingTime = Math.round((performance.now() - startTime) * 100) / 100;
            console.error(`[QUERY_ERROR] fetchMessages - Error fetching from simplified path:`, {
              error: error.message,
              processingTime,
              path: collectionPaths.messages
            });
            firestoreMessages = [];
            activeCollectionPath = null;
          }
        }

        // Save unsubscribe function for cleanup
        if (unsubscribeNested) {
          dispatch(setMessagesSnapshotUnsubscribe(unsubscribeNested));
        }
      }

      console.log('fetchMessages - Final result:', {
        nestedMessagesCount: result.length,
        firestoreMessagesCount: firestoreMessages.length,
        activeCollectionPath,
        messageFromFirestore: !!messageFromFirestore
      });

      return {
        nestedMessages: result,
        messages: firestoreMessages,
        messageFromFirestore,
        activeCollectionPath
      };
    } catch (error) {
      console.error("Error fetching messages:", error);
      return rejectWithValue(error.message);
    }
  }
);

export const fetchWhatsAppMessages = createAsyncThunk(
  "metaBusinessSuite/fetchWhatsAppMessages",
  async ({ thread, selectedPhone }, { rejectWithValue, dispatch, getState }) => {
    if (!thread || !selectedPhone) {
      return { apiMessages: [] };
    }

    try {
      // Fetch messages from API
      const result = await metaService.getMessagesForWhatsappChatApi({
        id: thread?.id,
        signal: null,
      });

      if (result?.data) {
        // Get the messages from the API response
        const apiMessages = result.data;

        // Update the selectedWhatsappChat with the latest timestamp
        if (apiMessages.length > 0) {
          const latestMessage = apiMessages.sort((a, b) =>
            new Date(b.created_at || b.timestamp) - new Date(a.created_at || b.timestamp)
          )[0];

          // Update the selected WhatsApp chat with the new timestamp
          dispatch(updateSelectedWhatsappChat((prevChat) => {
            if (!prevChat) return prevChat;
            const updatedChat = {
              ...prevChat,
              updated_at: latestMessage.created_at || latestMessage.timestamp || new Date().toISOString()
            };
            return updatedChat;
          }));
        }

        // Sort API messages by timestamp
        apiMessages.sort((a, b) => new Date(a.created_at || a.timestamp) - new Date(b.created_at || b.timestamp));

        // Transform API messages to match our format (same as Context API)
        const formattedApiMessages = apiMessages.map(msg => {
          const formattedMsg = {
            id: msg.id || `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            message_id: msg.id || msg.message_id,
            created_time: msg.created_at || msg.timestamp,
            // Preserve the message exactly as received, including all spaces
            message: msg.message,
            // Add URL field for image messages
            url: msg.type === "image" ? msg.url : msg.message,
            type: msg.type || "text",
            sender: msg.from === selectedPhone?.id ? normalizePhoneNumber(selectedPhone?.display_phone_number) : normalizePhoneNumber(thread?.sender_phone_number),
            recipient: msg.from === selectedPhone?.id ? normalizePhoneNumber(thread?.sender_phone_number) : normalizePhoneNumber(selectedPhone?.display_phone_number)
          };

          return formattedMsg;
        });

        // Set initial messages from API immediately
        dispatch(setWhatsappChatMessages(formattedApiMessages));

        // Also update the chat list with the latest message
        if (formattedApiMessages.length > 0) {
          const latestMessage = formattedApiMessages[formattedApiMessages.length - 1];

          // Update the selected WhatsApp chat with the latest message
          dispatch(updateSelectedWhatsappChat((prevChat) => {
            if (!prevChat) return prevChat;
            return {
              ...prevChat,
              last_message: {
                message: latestMessage.message,
                from: latestMessage.sender,
                type: latestMessage.type || "text",
                message_id: latestMessage.message_id,
                timestamp: Math.floor(new Date(latestMessage.created_time).getTime() / 1000).toString(),
                created_at: latestMessage.created_time,
                updated_at: latestMessage.created_time,
                url: latestMessage.url || null
              },
              updated_time: latestMessage.created_time,
            };
          }));
        }

        // Use simplified path structure for WhatsApp messages
        const chatType = 'whatsapp';

        // Get selectedPage from Redux state
        const state = getState().metaBusinessSuite;
        const selectedPage = state.selectedPage;

        const collectionPaths = getCollectionPaths(thread, chatType, selectedPage?.id);

        console.log("FetchWhatsAppMessages - Using simplified path structure:", {
          rawPhoneNumber: thread?.sender_phone_number,
          messagesPath: collectionPaths.messages,
          commentsPath: collectionPaths.comments,
          pageId: selectedPage?.id,
          thread
        });

        // Set up Firestore listener for real-time updates using simplified path structure
        let unsubscribe = null;

        if (collectionPaths.messages) {
          try {
            const nestedCollectionRef = collection(db, ...collectionPaths.messages.split('/'));
            const nestedQuery = query(
              nestedCollectionRef,
              orderBy("created_time", "asc")
            );

            console.log(`[QUERY_OPTIMIZATION] Setting up WhatsApp listener for path: ${collectionPaths.messages}`);

            unsubscribe = onSnapshot(nestedQuery, (snapshot) => {
              const startTime = performance.now();

              console.log("[QUERY_PERFORMANCE] Firebase listener triggered for WhatsApp. Changes:", snapshot.docChanges().length);

              // Log document changes for debugging
              snapshot.docChanges().forEach((change) => {
                console.log(`Document ${change.type}:`, change.doc.id, change.doc.data());
              });

              const nestedMessages = snapshot.docs.map((doc) => {
                const data = doc.data();
                return {
                  id: doc.id,
                  message_id: data?.message_id || doc.id,
                  created_time: data?.created_time,
                  message: data?.message,
                  type: data?.type || "text",
                  sender: data?.sender,
                  sender_name: data?.sender_name,
                  recipient: data?.recipient,
                  url: data?.url || null
                };
              });

              const processingTime = Math.round((performance.now() - startTime) * 100) / 100;
              console.log("[QUERY_PERFORMANCE] WhatsApp Firebase messages processed:", {
                messagesCount: nestedMessages.length,
                changes: snapshot.docChanges().length,
                processingTime,
                path: collectionPaths.messages
              });

              // If we have new messages, update the selectedWhatsappChat timestamp
              if (nestedMessages.length > 0) {
                const latestMessage = nestedMessages.sort((a, b) =>
                  new Date(b.created_time) - new Date(a.created_time)
                )[0];

                dispatch(updateSelectedWhatsappChat((prevChat) => {
                  if (!prevChat) return prevChat;
                  return {
                    ...prevChat,
                    updated_at: latestMessage.created_time
                  };
                }));
              }

              // Update whatsappChatMessages state – merging & sorting handled inside reducer
              dispatch(updateWhatsappChatMessages(nestedMessages));
            }, (error) => {
              console.error("[QUERY_ERROR] Firebase listener error for WhatsApp messages:", error);
              // Graceful degradation - continue without real-time updates
              console.warn("WhatsApp messages will not receive real-time updates due to listener error");
            });
          } catch (error) {
            console.error("[QUERY_ERROR] Error setting up WhatsApp Firebase listener:", error);
            // Graceful degradation - continue without real-time updates
            unsubscribe = null;
          }
        } else {
          console.warn("[QUERY_OPTIMIZATION] Unable to set up WhatsApp Firebase listener - invalid path");
        }

        const returnValue = {
          apiMessages: formattedApiMessages,
          updatedChat: thread,
          unsubscribe
        };

        console.log("FetchWhatsAppMessages returning:", {
          apiMessagesCount: formattedApiMessages.length,
          hasUnsubscribe: !!unsubscribe,
          messagesPath: collectionPaths.messages
        });

        return returnValue;
      } else {
        return { apiMessages: [], updatedChat: thread, unsubscribe: null };
      }
    } catch (error) {
      console.error("Error fetching WhatsApp messages:", error);
      return rejectWithValue(error.message);
    }
  }
);

// Fetch WhatsApp latest messages with Firebase listener
export const fetchWhatsAppLatestMessages = createAsyncThunk(
  "metaBusinessSuite/fetchWhatsAppLatestMessages",
  async ({ selectedPhone }, { rejectWithValue, getState, dispatch }) => {
    try {
      if (!selectedPhone) {
        return null;
      }

      // Get the business phone number in a consistent format
      const businessPhoneNumber = selectedPhone?.display_phone_number.trim().replace(/^\+|\s+/g, '');

      // Query WhatsApp documents where the sender or recipient matches the selected phone
      const whatsAppQuery = query(
        collection(db, "whatsApp"),
        orderBy("created_time", "desc")
      );

      // Set up polling to check for new messages every 10 seconds
      const startPolling = () => {
        // Clean up any existing polling interval
        const state = getState().metaBusinessSuite;
        if (state.pollingInterval) {
          clearInterval(state.pollingInterval);
        }

        // Store the polling interval in Redux state (null for now since polling is disabled)
        dispatch(setPollingInterval(null));
      };

      return new Promise((resolve) => {
        const unsubscribe = onSnapshot(whatsAppQuery, (snapshot) => {
          const firebaseChats = snapshot.docs
            .map((doc) => {
              const data = doc.data();

              // Only include chats where this business phone is the sender or recipient
              if (data.sender !== businessPhoneNumber && data.recipient !== businessPhoneNumber) {
                return null;
              }

              // The document ID is the customer's phone number
              const customerPhoneNumber = doc.id;
              // For chat display, we always want to show the customer's name/number
              const customerName = data.sender_name || customerPhoneNumber;

              // Determine the correct sender_name based on who sent the message
              let correctSenderName;
              if (data.sender === businessPhoneNumber) {
                // Business sent the message, use business name
                correctSenderName = data.sender_name || "Business";
              } else {
                // Customer sent the message, use customer name or phone number
                // For incoming messages from customers, we need to preserve their name
                correctSenderName = data.sender_name || customerPhoneNumber;
              }

              const firebaseChat = {
                // Store the original API ID if available, otherwise use document ID or generate one
                originalApiId: data.originalApiId || doc.id || `temp_${Date.now()}`, // Ensure we always have an ID
                sender_phone_number: customerPhoneNumber,
                // Always use customer name for chat display
                sender_name: customerName,
                // Store the actual sender name separately for the last message
                last_message_sender_name: correctSenderName,
                ...data,
                created_time: data.created_time,
                updated_time: data.created_time,
                created_at: data.created_time,
                updated_at: data.created_time,
                last_message: {
                  message: data.message,
                  from: data.sender,
                  type: data.type || "text",
                  message_id: data.message_id,
                  timestamp: Math.floor(new Date(data.created_time).getTime() / 1000).toString(),
                  created_at: data.created_time,
                  updated_at: data.created_time,
                  url: data.url || null
                },
                // Add a flag to identify this as a Firebase document
                fromFirebase: true
              };
              return firebaseChat;
            })
            .filter(chat => chat !== null); // Remove null entries

          // Dispatch the Redux action to update the state
          dispatch(setWhatsappChats(firebaseChats));

          // Also update latest messages for the merged selector - FIXED: Merge instead of replace
          const latestMessages = firebaseChats.map(chat => ({
            id: chat.sender_phone_number,
            sender: chat.sender,
            sender_name: chat.sender_name,
            recipient: chat.recipient,
            message: chat.message,
            message_id: chat.message_id,
            created_time: chat.created_time,
            updated_time: chat.created_time,
            created_at: chat.created_time,
            type: chat.type || "text",
            url: chat.url || null
          }));

          // Merge with existing latest messages instead of replacing
          dispatch(updateLatestMessages((prevLatestMessages) => {
            if (!prevLatestMessages) return latestMessages;

            // Create a map of existing messages by ID for quick lookup
            const existingMessagesMap = new Map();
            prevLatestMessages.forEach(msg => {
              const key = msg.id || msg.sender || msg.recipient;
              if (key) {
                existingMessagesMap.set(key, msg);
              }
            });

            // Merge new messages with existing ones
            latestMessages.forEach(newMsg => {
              const key = newMsg.id || newMsg.sender || newMsg.recipient;
              if (key) {
                const existingMsg = existingMessagesMap.get(key);
                if (existingMsg) {
                  // Update existing message if new one is more recent
                  const existingDate = new Date(existingMsg.created_time || existingMsg.created_at);
                  const newDate = new Date(newMsg.created_time || newMsg.created_at);
                  if (newDate > existingDate) {
                    existingMessagesMap.set(key, newMsg);
                  }
                } else {
                  // Add new message
                  existingMessagesMap.set(key, newMsg);
                }
              }
            });

            // Convert back to array and sort
            const result = Array.from(existingMessagesMap.values()).sort((a, b) => {
              const dateA = parseDate(a.created_time || a.created_at);
              const dateB = parseDate(b.created_time || b.created_at);
              return dateB - dateA; // Sort in descending order
            });

            return result;
          }));

          // Start polling if not already started
          const state = getState().metaBusinessSuite;
          if (!state.pollingInterval) {
            startPolling();
          }

          resolve({
            firebaseChats, unsubscribe: () => {
              // Clean up both the Firestore listener and the polling interval
              unsubscribe();
              const state = getState().metaBusinessSuite;
              if (state.pollingInterval) {
                clearInterval(state.pollingInterval);
                dispatch(setPollingInterval(null));
              }
            }
          });
        });
      });
    } catch (error) {
      console.error("Error fetching WhatsApp latest messages:", error);
      return rejectWithValue(error.message);
    }
  }
);

// Listen to any new document inside whatsApp/*/messages for the selected business phone
export const listenToAllWhatsappMessages = createAsyncThunk(
  "metaBusinessSuite/listenToAllWhatsappMessages",
  async ({ selectedPhone }, { dispatch, getState }) => {
    if (!selectedPhone) return;

    // Use consistent chat ID resolution for WhatsApp
    const chatType = 'whatsapp';
    const businessNumber = getChatIdentifier({ sender_phone_number: selectedPhone.display_phone_number }, chatType);

    if (!businessNumber) {
      console.error('listenToAllWhatsappMessages: Unable to determine business number');
      return;
    }

    console.log('listenToAllWhatsappMessages - Using consistent chat ID resolution:', {
      businessNumber,
      originalPhone: selectedPhone.display_phone_number
    });

    // Clean previous listener if any
    const prevUnsub = getState().metaBusinessSuite.latestMessagesUnsubscribe;
    if (prevUnsub) {
      try { prevUnsub(); } catch (e) { /* noop */ }
    }

    // Use optimized WhatsApp collection group query
    console.log('[QUERY_OPTIMIZATION] Setting up optimized WhatsApp collection group listener for business:', businessNumber);

    const unsubscribe = listenToWhatsAppMessagesOptimized(businessNumber, (messages) => {
      const startTime = performance.now();

      // Group messages by customer phone for efficient processing
      const messagesByCustomer = new Map();

      messages.forEach((message) => {
        const customerPhone = message.chatId;
        if (!messagesByCustomer.has(customerPhone)) {
          messagesByCustomer.set(customerPhone, []);
        }
        messagesByCustomer.get(customerPhone).push(message);
      });

      // Process each customer's messages
      messagesByCustomer.forEach((customerMessages, customerPhone) => {
        const latestMessage = customerMessages.sort((a, b) =>
          new Date(b.created_time) - new Date(a.created_time)
        )[0];

        if (latestMessage) {
          // 1) merge into latestMessages
          dispatch(updateLatestMessages((prev = []) => {
            const rest = (prev || []).filter(
              (m) => !(m.sender === customerPhone || m.recipient === customerPhone)
            );
            return [
              {
                id: customerPhone,
                sender: latestMessage.sender,
                recipient: latestMessage.recipient,
                message: latestMessage.message,
                message_id: latestMessage.message_id,
                created_time: latestMessage.created_time,
                type: latestMessage.type || "text",
                url: latestMessage.url || null,
              },
              ...rest,
            ];
          }));

          // 2) update whatsappChats preview list
          dispatch(updateWhatsappChatsWithFirebase((prevChats = []) => {
            const others = prevChats.filter((c) => c.sender_phone_number !== customerPhone);
            const existing = prevChats.find((c) => c.sender_phone_number === customerPhone);
            const base = existing || {
              sender_phone_number: customerPhone,
              sender_name: latestMessage.sender_name || customerPhone,
            };

            return [
              {
                ...base,
                last_message: {
                  message: latestMessage.message,
                  from: latestMessage.sender,
                  type: latestMessage.type || "text",
                  message_id: latestMessage.message_id,
                  timestamp: Math.floor(new Date(latestMessage.created_time).getTime() / 1000).toString(),
                  created_at: latestMessage.created_time,
                  updated_at: latestMessage.created_time,
                  url: latestMessage.url || null
                },
                updated_time: latestMessage.created_time,
                created_time: latestMessage.created_time,
                created_at: latestMessage.created_time,
                updated_at: latestMessage.created_time,
                fromFirebase: true
              },
              ...others,
            ];
          }));
        }
      });

      const processingTime = Math.round((performance.now() - startTime) * 100) / 100;
      console.log('[QUERY_PERFORMANCE] WhatsApp collection group processed:', {
        totalMessages: messages.length,
        uniqueCustomers: messagesByCustomer.size,
        processingTime
      });
    }, { limit: 100, orderDirection: 'desc' });

    // Store the unsubscribe function
    dispatch(setLatestMessagesUnsubscribe(unsubscribe));

    return { unsubscribe };

  }
);

// Select chat async thunk with Firebase cleanup
export const selectChatAsync = createAsyncThunk(
  "metaBusinessSuite/selectChatAsync",
  async ({ thread }, { dispatch, getState }) => {
    console.log(thread);

    try {
      // Reset disabled chat state when switching to a new chat
      dispatch(setDisabledChat(false));

      // Clean up previous listener
      const state = getState().metaBusinessSuite;
      if (state.messagesSnapshotUnsubscribe) {
        state.messagesSnapshotUnsubscribe();
      }

      // Clean up polling interval
      if (state.pollingInterval) {
        clearInterval(state.pollingInterval);
        dispatch(setPollingInterval(null));
      }

      // Delete the nested "messages" collection for the selected chat using both old and new paths
      if (thread && thread.id) {
        console.log('Deleting nested messages for chat:', thread.id);

        // Get both current and legacy paths for cleanup during migration
        const chatType = determineChatType(thread);
        const dualWritePaths = getDualWritePaths(thread, chatType);

        console.log('Cleanup paths:', {
          current: dualWritePaths.current.messages,
          legacy: dualWritePaths.legacy.messages,
          requiresMigration: dualWritePaths.requiresMigration
        });

        const deletePromises = [];

        // Clean up new (current) path
        if (dualWritePaths.current.messages) {
          try {
            const [mainCollection, chatId, subCollection] = dualWritePaths.current.messages.split('/');
            const nestedCollectionRef = collection(db, mainCollection, chatId, subCollection);
            const nestedDocs = await getDocs(nestedCollectionRef);

            console.log(`Found ${nestedDocs.docs.length} messages to delete in current path: ${dualWritePaths.current.messages}`);

            const currentDeletePromises = nestedDocs.docs.map((doc) => deleteDoc(doc.ref));
            deletePromises.push(...currentDeletePromises);
          } catch (error) {
            console.error('Error cleaning up current path:', error);
          }
        }

        // Clean up legacy path (if different and migration required)
        if (dualWritePaths.requiresMigration && dualWritePaths.legacy.messages &&
          dualWritePaths.legacy.messages !== dualWritePaths.current.messages) {
          try {
            const [mainCollection, chatId, subCollection] = dualWritePaths.legacy.messages.split('/');
            const nestedCollectionRef = collection(db, mainCollection, chatId, subCollection);
            const nestedDocs = await getDocs(nestedCollectionRef);

            console.log(`Found ${nestedDocs.docs.length} messages to delete in legacy path: ${dualWritePaths.legacy.messages}`);

            const legacyDeletePromises = nestedDocs.docs.map((doc) => deleteDoc(doc.ref));
            deletePromises.push(...legacyDeletePromises);
          } catch (error) {
            console.error('Error cleaning up legacy path:', error);
          }
        }

        if (deletePromises.length > 0) {
          await Promise.all(deletePromises);
          console.log(`Nested messages deleted successfully from ${deletePromises.length} documents`);
        } else {
          console.log('No messages found to delete');
        }
      }

      // Fetch messages for the selected chat
      await dispatch(fetchMessages({ thread }));

      // Load comments for the selected chat using consistent chat ID resolution
      const chatType = determineChatType(thread);
      const firebaseChatId = getChatIdentifier(thread, chatType);

      await dispatch(loadChatCommentsHybrid({
        backendChatId: thread.id,
        firebaseChatId: firebaseChatId, // Use consistent chat ID resolution (backend chat ID, not participant ID)
        chatType: chatType
      }));

      return thread;
    } catch (error) {
      console.error("Error selecting chat:", error);
      throw error;
    }
  }
);

// Select WhatsApp chat async thunk with Firebase cleanup
export const selectWhatsappChatAsync = createAsyncThunk(
  "metaBusinessSuite/selectWhatsappChatAsync",
  async ({ thread, selectedPhone }, { dispatch, getState }) => {
    try {
      // Reset disabled chat state when switching to a new chat
      dispatch(setDisabledChat(false));

      // Clean up previous listener
      const state = getState().metaBusinessSuite;
      if (state.messagesSnapshotUnsubscribe) {
        state.messagesSnapshotUnsubscribe();
      }

      // Clean up polling interval
      if (state.pollingInterval) {
        clearInterval(state.pollingInterval);
        dispatch(setPollingInterval(null));
      }

      // Delete the nested "messages" collection for the selected WhatsApp chat
      if (thread && thread.sender_phone_number) {
        const senderPhoneId = normalizePhoneNumber(thread.sender_phone_number);

        if (senderPhoneId) {
          const nestedCollectionRef = collection(
            db,
            "whatsApp",
            senderPhoneId,
            "messages"
          );

          const nestedDocs = await getDocs(nestedCollectionRef);
          const deletePromises = nestedDocs.docs.map((doc) => deleteDoc(doc.ref));

          await Promise.all(deletePromises);
        }
      }

      // Ensure the thread has the correct ID for fetching messages
      const threadWithId = {
        ...thread,
        id: thread.originalApiId || thread.id // Use originalApiId if available, otherwise use id
      };

      // Load comments for the selected WhatsApp chat
      await dispatch(loadChatCommentsHybrid({
        backendChatId: thread.id || thread.sender_phone_number,
        firebaseChatId: normalizePhoneNumber(thread.sender_phone_number),
        chatType: "whatsapp"
      }));

      return thread;
    } catch (error) {
      console.error("Error selecting WhatsApp chat:", error);
      throw error;
    }
  }
);

// Async thunk to update Firebase parent document for incoming messages
export const updateFirebaseParentDocument = createAsyncThunk(
  "metaBusinessSuite/updateFirebaseParentDocument",
  async ({ selectedChat, latestMessage }, { getState, rejectWithValue }) => {
    try {
      if (!selectedChat || !latestMessage) {
        return { success: false, reason: 'Missing required parameters' };
      }

      // Get selectedPage from Redux state
      const state = getState().metaBusinessSuite;
      const selectedPage = state.selectedPage;

      // Determine chat type and get simplified collection paths
      const chatType = determineChatType(selectedChat);

      if (!selectedPage?.id) {
        return { success: false, reason: 'No page selected' };
      }

      // Use simplified path structure: ${pageId}/${sender}/messages
      const collectionPaths = getCollectionPaths(selectedChat, chatType, selectedPage.id);

      if (!collectionPaths.messages) {
        return { success: false, reason: 'Could not determine collection paths' };
      }

      // Prepare message data for simplified structure
      const messageData = {
        id: latestMessage.id || `incoming_${Date.now()}`,
        message: latestMessage.message,
        sender: latestMessage.sender || latestMessage.from?.id,
        recipient: latestMessage.recipient,
        created_time: latestMessage.created_time || new Date().toISOString(),
        type: latestMessage.type || 'text'
      };

      console.log('[INCOMING_MESSAGE] Writing message to simplified path:', {
        messagesPath: collectionPaths.messages,
        pageId: selectedPage?.id,
        chatType,
        messageData
      });

      // Write the message to the simplified path: ${pageId}/${sender}/messages
      const messageRef = doc(db, ...collectionPaths.messages.split('/'), messageData.id);
      await setDoc(messageRef, messageData);

      console.log('[INCOMING_MESSAGE] Firebase message written successfully to simplified path');

      return {
        success: true,
        messagesPath: collectionPaths.messages,
        messageData
      };

    } catch (error) {
      console.error('[INCOMING_MESSAGE] Error updating Firebase parent document:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Async thunk to handle incoming messages with Firebase parent document update
export const handleIncomingMessage = createAsyncThunk(
  "metaBusinessSuite/handleIncomingMessage",
  async ({ messages, selectedChat }, { getState, dispatch, rejectWithValue }) => {
    try {
      if (!messages || !Array.isArray(messages) || messages.length === 0) {
        return { success: false, reason: 'No messages provided' };
      }

      // First, update the messages in Redux state
      dispatch(updateMessages(messages));

      // Get the latest message for parent document update
      const latestMessage = messages[messages.length - 1];

      if (!selectedChat) {
        // If no selectedChat provided, try to get it from state
        const state = getState().metaBusinessSuite;
        selectedChat = state.selectedChat;
      }

      if (!selectedChat) {
        console.warn('[INCOMING_MESSAGE] No selected chat available for parent document update');
        return { success: true, reason: 'Messages updated but no parent document update' };
      }

      // Update the Firebase parent document
      const result = await dispatch(updateFirebaseParentDocument({
        selectedChat,
        latestMessage
      }));

      if (result.type.endsWith('/fulfilled')) {
        console.log('[INCOMING_MESSAGE] Successfully handled incoming message');
        return {
          success: true,
          messagesCount: messages.length,
          parentDocumentUpdated: true
        };
      } else {
        console.warn('[INCOMING_MESSAGE] Parent document update failed:', result.payload);
        return {
          success: true,
          messagesCount: messages.length,
          parentDocumentUpdated: false,
          reason: result.payload
        };
      }

    } catch (error) {
      console.error('[INCOMING_MESSAGE] Error handling incoming message:', error);
      return rejectWithValue(error.message);
    }
  }
);

export const sendMessage = createAsyncThunk(
  "metaBusinessSuite/sendMessage",
  async ({ data, selectedChat }, { rejectWithValue, dispatch, getState }) => {
    try {
      const response = await metaService.sendMessageApi(data);
      // Check for 24-hour window error in the response
      if (response && typeof response === 'object') {
        // Check if response has an error property
        if (response.error) {
          // Check for 24-hour window error (code 10)
          if (response.error.code === 10 ||
            response.error.error_subcode === 2018278 ||
            (response.error.message && response.error.message.includes("(#10)")) ||
            (response.error.message && response.error.message.toLowerCase().includes("outside the allowed window"))) {
            dispatch(setDisabledChat(true));
            return false; // Return false to indicate error
          }

          // Handle other API errors
          return false; // Return false to indicate error
        }
      }

      const currentDate = new Date();
      const formattedDate = currentDate.toISOString();

      // Ensure we have a valid selectedChat before proceeding
      if (!selectedChat || !selectedChat.participants || !selectedChat.participants.data) {
        console.error("Invalid selectedChat object:", selectedChat);
        return false;
      }

      // Safely extract recipient and sender IDs with fallbacks
      let recipient, sender;

      try {
        recipient = selectedChat?.flage === "instagram"
          ? (selectedChat.participants.data[1]?.id || "").toString()
          : (selectedChat.participants.data[0]?.id || "").toString();

        sender = selectedChat?.flage === "instagram"
          ? (selectedChat.participants.data[0]?.id || "").toString()
          : (selectedChat.participants.data[1]?.id || "").toString();
      } catch (error) {
        console.error("Error extracting recipient/sender IDs:", error);
        return false;
      }

      // Validate recipient ID
      if (!recipient) {
        console.error("Invalid recipient ID");
        return false;
      }

      // Determine message type based on data
      const messageType = data.get("type") ||
        (response?.file_url ?
          (response.file_url.match(/\.(jpeg|jpg|gif|png)$/) ? "image" :
            response.file_url.match(/\.(mp4|mov|wmv|avi)$/) ? "video" : "file")
          : "text");

      const newMessage = {
        id: response?.message_id || `msg_${Date.now()}`,
        type: messageType,
        sender: sender,
        recipient: recipient,
        message: response?.file_url ? response?.file_url : data.get("message"),
        created_time: formattedDate,
      };

      // --- Firestore updates with dual write support ---

      try {
        // Determine chat type and get simplified collection paths
        const chatType = determineChatType(selectedChat);

        // Get selectedPage from Redux state
        const state = getState().metaBusinessSuite;
        const selectedPage = state.selectedPage;

        if (!selectedPage?.id) {
          console.error('SendMessage: No page selected, cannot determine collection paths');
          return rejectWithValue('No page selected');
        }

        // Use simplified path structure: ${pageId}/${sender}/messages
        const collectionPaths = getCollectionPaths(selectedChat, chatType, selectedPage.id);

        console.log('SendMessage - Using simplified collection paths:', {
          chatType,
          messagesPath: collectionPaths.messages,
          selectedChatId: selectedChat?.id,
          pageId: selectedPage?.id,
          sender,
          recipient
        });

        if (!collectionPaths.messages) {
          console.error('SendMessage: Could not determine collection paths');
          return rejectWithValue('Could not determine collection paths');
        }

        // Write message to simplified path: ${pageId}/${sender}/messages
        try {
          const messageRef = doc(db, ...collectionPaths.messages.split('/'), newMessage.id);
          await setDoc(messageRef, newMessage);

          console.log('SendMessage - Message write successful:', `${collectionPaths.messages}/${newMessage.id}`);
        } catch (error) {
          console.error('SendMessage - Message write failed:', error);
          throw error;
        }

        // Update latest messages state using sender ID for consistency with simplified structure
        const senderId = getSenderId(selectedChat, chatType);
        dispatch(updateLatestMessages((prevLatestMessages) => {
          if (!prevLatestMessages) return prevLatestMessages;

          const updatedLatestMessages = prevLatestMessages.map(msg => {
            // Match using sender ID for consistency with simplified structure
            if (msg.senderId === senderId || msg.sender === senderId ||
              msg.recipient === recipient || msg.sender === recipient) {
              return {
                ...msg,
                message: newMessage.message,
                type: messageType,
                created_time: formattedDate,
                updated_time: formattedDate,
                senderId: senderId,
                sender: senderId
              };
            }
            return msg;
          });

          // If no existing message found, add a new one
          const existingMessage = updatedLatestMessages.find(msg =>
            msg.senderId === senderId || msg.sender === senderId ||
            msg.recipient === recipient || msg.sender === recipient
          );

          if (!existingMessage) {
            updatedLatestMessages.push({
              id: newMessage.id,
              senderId: senderId,
              sender: senderId,
              recipient: recipient,
              message: newMessage.message,
              type: messageType,
              created_time: formattedDate,
              updated_time: formattedDate
            });
          }

          return updatedLatestMessages.sort((a, b) =>
            parseDate(b.created_time) - parseDate(a.created_time)
          );
        }));

        // Update the appropriate chat list based on activeFilter
        const updateChatList = (prevChats) => {
          if (!prevChats) return prevChats;

          const updatedChats = prevChats.map(chat => {
            if (chat.id === selectedChat.id) {
              return {
                ...chat,
                updated_time: formattedDate,
                message: newMessage.message,
                type: messageType
              };
            }
            return chat;
          });

          return updatedChats.sort((a, b) =>
            parseDate(b.updated_time) - parseDate(a.updated_time)
          );
        };

        if (selectedChat?.flage === "instagram") {
          dispatch(updateInstagramChats(updateChatList));
        } else {
          dispatch(updateMessengerChats(updateChatList));
        }

        return true; // Return true to indicate success

      } catch (error) {
        console.error("Error updating Firebase:", error);

        // Log Firebase write failure
        logDualWriteOperation('sendMessage_firebase_error', false, error, {
          chatType: determineChatType(selectedChat),
          selectedChatId: selectedChat?.id,
          recipient,
          sender
        });

        return false;
      }
    } catch (error) {
      console.error("Error in sendMessage:", error);

      // Check for 24-hour window error in the caught error
      if (error && error.response && error.response.data && error.response.data.error) {
        const apiError = error.response.data.error;
        if (apiError.code === 10 ||
          apiError.error_subcode === 2018278 ||
          (apiError.message && apiError.message.includes("(#10)")) ||
          (apiError.message && apiError.message.toLowerCase().includes("outside the allowed window"))) {
          dispatch(setDisabledChat(true));
          return false;
        }
      }

      return false;
    }
  }
);

export const sendWhatsMessage = createAsyncThunk(
  "metaBusinessSuite/sendWhatsMessage",
  async ({ data, selectedPhone, selectedWhatsAccount, selectedChat, selectedWhatsappChat }, { rejectWithValue, dispatch, getState }) => {
    try {
      const response = await metaService.sendWhatsMessageApi(data);

      const fileUrl = response?.file_url || null;

      if (response?.error) {
        console.error("Error in WhatsApp API response:", response.error);
        return false;
      }

      // Create a new message object
      const currentDate = new Date();
      const formattedDate = currentDate.toISOString();

      // Business phone number (from the selected phone in context)
      const senderPhone = normalizePhoneNumber(selectedPhone?.display_phone_number) || '';

      // Determine customer phone number from either selectedWhatsappChat or selectedChat
      const customerPhoneNumberRaw = selectedWhatsappChat?.sender_phone_number || selectedChat?.sender_phone_number || '';
      const recipientPhone = normalizePhoneNumber(customerPhoneNumberRaw);

      if (!recipientPhone) {
        throw new Error('Invalid recipient phone number');
      }

      console.log("SendWhatsMessage - Customer phone processing:", {
        raw: customerPhoneNumberRaw,
        processed: recipientPhone,
        selectedWhatsappChat: selectedWhatsappChat?.sender_phone_number,
        selectedChat: selectedChat?.sender_phone_number
      });

      console.log("SendWhatsMessage - Will save message to Firebase path:", `whatsApp/${recipientPhone}/messages`);

      const senderName = selectedWhatsAccount?.name || "Business";
      const messageText = data.get("text") || "";
      const messageType = data.get("type") || "text";

      // For media messages (image, video, audio, sticker), store the URL in both message and url fields
      const isMediaMessage = messageType === "image" ||
        messageType === "video" ||
        messageType === "audio" ||
        messageType === "sticker";

      // Define senderId and recipientId for use in the function
      const senderId = senderPhone;
      const recipientId = recipientPhone;

      // Create the message object with the URL field for media messages
      const newMessage = {
        id: response?.messages?.[0]?.id || response?.message_id || `whats_${Date.now()}`,
        message_id: response?.messages?.[0]?.id || response?.message_id || `whats_${Date.now()}`,
        type: messageType,
        sender: senderPhone,
        sender_name: senderName,
        recipient: recipientPhone,
        message: isMediaMessage ? fileUrl : messageText,
        // Add URL field for media messages
        url: isMediaMessage ? fileUrl : null,
        created_time: formattedDate,
        // Add MIME type for audio messages
        mime_type: messageType === "audio" ? "audio/mp3" : null
      };

      try {
        // Update the selectedWhatsappChat with the new timestamp
        dispatch(updateSelectedWhatsappChat((prevChat) => {
          if (!prevChat) return prevChat;
          const updatedChat = {
            ...prevChat,
            updated_at: formattedDate
          };

          return updatedChat;
        }));

        // Use simplified path structure for WhatsApp messages
        const chatType = 'whatsapp';

        // Get selectedPage from Redux state
        const state = getState().metaBusinessSuite;
        const selectedPage = state.selectedPage;

        if (!selectedPage?.id) {
          throw new Error('No page selected for WhatsApp message');
        }

        // Use simplified path structure: ${pageId}/${sender}/messages
        const collectionPaths = getCollectionPaths(selectedWhatsappChat || selectedChat, chatType, selectedPage.id);

        console.log('SendWhatsMessage - Using simplified collection paths:', {
          chatType,
          messagesPath: collectionPaths.messages,
          pageId: selectedPage.id,
          recipientPhone
        });

        if (!collectionPaths.messages) {
          throw new Error('Could not determine collection paths for WhatsApp message');
        }

        // Write message to simplified path: ${pageId}/${sender}/messages
        try {
          const messageRef = doc(db, ...collectionPaths.messages.split('/'), newMessage.id);
          await setDoc(messageRef, newMessage);

          console.log("SendWhatsMessage - Write successful:", `${collectionPaths.messages}/${newMessage.id}`);
        } catch (error) {
          console.error('SendWhatsMessage - Message write failed:', error);
          throw error;
        }

        // Immediately merge the sent message into the open chat thread
        console.log("About to add message to chat immediately:", newMessage);
        dispatch(updateWhatsappChatMessages([newMessage]));

        console.log("Sent message added to chat immediately:", newMessage);

        // Verify the message was added to state
        const currentState = getState().metaBusinessSuite;
        console.log("Current whatsappChatMessages count after adding:", currentState.whatsappChatMessages.length);
        console.log("Last message in chat:", currentState.whatsappChatMessages[currentState.whatsappChatMessages.length - 1]);

        // Additional debug logging to verify Firebase listener path matches storage path
        const { selectedWhatsappChat: currentSelectedChat } = getState().metaBusinessSuite;
        if (currentSelectedChat) {
          const listenerPhoneId = normalizePhoneNumber(currentSelectedChat.sender_phone_number);
          console.log("Firebase listener should be active for path:", `whatsApp/${listenerPhoneId}/messages`);
          console.log("Message stored at path:", `whatsApp/${recipientPhone}/messages`);
          console.log("Paths match:", listenerPhoneId === recipientPhone);

          if (listenerPhoneId !== recipientPhone) {
            console.error("PHONE NUMBER MISMATCH - Firebase listener won't pick up the message!");
            console.error("Listener phone:", listenerPhoneId);
            console.error("Storage phone:", recipientPhone);
            console.error("Selected chat:", currentSelectedChat);
          }
        }

        // Force a small delay to ensure Firebase write is complete before listener picks it up
        setTimeout(() => {
          console.log("Firebase write should be complete, listener should have triggered");
        }, 100);

        console.log("Message saved to Firebase:", {
          collection: `whatsApp/${recipientPhone}/messages`,
          messageId: newMessage.id,
          message: newMessage
        });

        // Update latest messages state using sender ID for consistency with simplified structure
        const senderId = getSenderId(selectedWhatsappChat || selectedChat, chatType);
        dispatch(updateLatestMessages((prevLatestMessages) => {
          if (!prevLatestMessages) return prevLatestMessages;

          const updatedLatestMessages = prevLatestMessages.map(msg => {
            // Match using sender ID for consistency with simplified structure
            if (msg.senderId === senderId || msg.sender === senderId) {
              return {
                ...msg,
                message: newMessage.message,
                message_id: newMessage.message_id,
                sender_name: senderName,
                created_time: formattedDate,
                updated_time: formattedDate,
                type: messageType,
                url: isMediaMessage ? fileUrl : null,
                senderId: senderId,
                sender: senderId
              };
            }
            return msg;
          });

          // If no existing message found, add a new one
          const existingMessage = updatedLatestMessages.find(msg =>
            msg.senderId === senderId || msg.sender === senderId
          );

          if (!existingMessage) {
            updatedLatestMessages.push({
              id: newMessage.id,
              senderId: senderId,
              sender: senderId,
              sender_name: senderName,
              recipient: recipientPhone,
              message: newMessage.message,
              message_id: newMessage.message_id,
              created_time: formattedDate,
              updated_time: formattedDate,
              created_at: formattedDate,
              type: messageType,
              url: isMediaMessage ? fileUrl : null
            });
          }

          const sortedMessages = updatedLatestMessages.sort((a, b) =>
            parseDate(b.created_time) - parseDate(a.created_time)
          );

          return sortedMessages;
        }));

        // Update WhatsApp chats list - FIXED: Ensure proper chat matching and sorting
        dispatch(updateWhatsappChatsWithFirebase((prevChats) => {
          if (!prevChats) return prevChats;

          const updatedChats = prevChats.map(chat => {
            // Match by sender_phone_number to ensure we update the correct chat
            if (chat.sender_phone_number === selectedWhatsappChat.sender_phone_number) {
              const updatedChat = {
                ...chat,
                // Preserve the original numeric id from the API
                id: chat.id, // This should be the numeric ID from the API
                updated_at: formattedDate,
                last_message: {
                  ...chat.last_message,
                  message: isMediaMessage ? fileUrl : messageText,
                  from: senderPhone,
                  type: messageType,
                  // Add URL field for media messages
                  url: isMediaMessage ? fileUrl : null,
                  message_id: newMessage.message_id,
                  timestamp: Math.floor(currentDate.getTime() / 1000).toString(),
                  created_at: formattedDate,
                  updated_at: formattedDate
                }
              };

              return updatedChat;
            }
            return chat;
          });

          // Sort by latest message time
          const sortedChats = updatedChats.sort((a, b) => {
            const timeA = a.last_message?.created_at
              ? parseDate(a.last_message.created_at)
              : a.updated_at
                ? parseDate(a.updated_at)
                : parseDate(a.created_at);

            const timeB = b.last_message?.created_at
              ? parseDate(b.last_message.created_at)
              : b.updated_at
                ? parseDate(b.updated_at)
                : parseDate(b.created_at);

            return timeB - timeA; // Sort in descending order (newest first)
          });

          return sortedChats;
        }));

        return true; // Return true to indicate success

      } catch (error) {
        console.error("Error sending WhatsApp message:", error);
        return false;
      }
    } catch (error) {
      console.error("Error in sendWhatsMessage:", error);

      // Log WhatsApp message send failure
      logDualWriteOperation('sendWhatsMessage_error', false, error, {
        chatType: 'whatsapp',
        selectedPhone: selectedPhone?.id,
        selectedWhatsappChat: selectedWhatsappChat?.sender_phone_number,
        messageType: data.get("type") || "text"
      });

      return rejectWithValue(error?.message || "Failed to send WhatsApp message");
    }
  }
);

// Fetch WhatsApp chats from API and sync with Firebase
export const fetchWhatsAppChatsFromAPI = createAsyncThunk(
  "metaBusinessSuite/fetchWhatsAppChatsFromAPI",
  async ({ selectedPhone }, { rejectWithValue, dispatch, getState }) => {
    try {
      if (!selectedPhone) {
        return [];
      }

      // Fetch chats from API
      const response = await metaService.getWhatsAppChatsForPageApi({
        id: selectedPhone.id,
      });

      if (!response?.data) {
        return [];
      }

      const apiChats = response.data;

      // Sync each API chat with Firebase
      for (const apiChat of apiChats) {
        try {
          const chatDocRef = doc(db, "whatsApp", apiChat.sender_phone_number);

          const docData = {
            sender: apiChat.last_message?.from === selectedPhone?.id ?
              selectedPhone?.display_phone_number.trim().replace(/^\+|\s+/g, '') :
              apiChat.sender_phone_number,
            sender_name: apiChat.sender_name,
            recipient: apiChat.last_message?.from === selectedPhone?.id ?
              apiChat.sender_phone_number :
              selectedPhone?.display_phone_number.trim().replace(/^\+|\s+/g, ''),
            message: apiChat.last_message?.message || "",
            message_id: apiChat.last_message?.message_id || apiChat.last_message?.id,
            created_time: apiChat.last_message?.created_at || apiChat.created_at,
            type: apiChat.last_message?.type || "text",
            url: apiChat.last_message?.url || null,
            originalApiId: apiChat.id // Store the API ID
          };

          await setDoc(chatDocRef, docData);
        } catch (error) {
          console.error('Error syncing API chat to Firebase:', apiChat.sender_phone_number, error);
        }
      }

      return apiChats;
    } catch (error) {
      console.error("Error fetching WhatsApp chats from API:", error);
      return rejectWithValue(error.message);
    }
  }
);

// Comments system async thunks
export const fetchComments = createAsyncThunk(
  "metaBusinessSuite/fetchComments",
  async ({ chatId, chatType }, { getState, rejectWithValue }) => {
    try {
      const { fetchComments: fetchCommentsService } = await import('../../services/comments');
      const { validateChatContext } = await import('../../utils/commentValidation');
      const { handleCommentError } = await import('../../utils/commentErrorHandling');

      // Get current user for permission validation
      const state = getState();
      const currentUser = state.auth?.user;
      const selectedPage = state.metaBusinessSuite?.selectedPage;

      // Validate chat context before making the request
      const chatValidation = validateChatContext(chatId, chatType);
      if (!chatValidation.isValid) {
        return rejectWithValue(`Invalid chat context: ${chatValidation.errors.join(', ')}`);
      }

      // Ensure pageId is a string
      const pageId = selectedPage?.id ? String(selectedPage.id) : null;

      const comments = await fetchCommentsService(chatId, chatType, currentUser, pageId);
      return comments;
    } catch (error) {
      console.error("Error fetching comments:", error);

      // Use centralized error handling
      const { handleCommentError } = await import('../../utils/commentErrorHandling');
      const commentError = handleCommentError(error, 'fetch comments', {
        showToast: false, // Don't show toast, let UI handle it
        throwError: false
      });

      return rejectWithValue(commentError.message);
    }
  }
);

// Hybrid Comments Actions
export const loadChatCommentsHybrid = createAsyncThunk(
  "metaBusinessSuite/loadChatCommentsHybrid",
  async ({ backendChatId, firebaseChatId, chatType }, { dispatch, getState, rejectWithValue }) => {
    try {
      const { loadChatComments } = await import('../../services/comments/hybridService');

      // Get selectedPage from Redux state
      const state = getState().metaBusinessSuite;
      const selectedPage = state.selectedPage;

      const result = await loadChatComments(
        backendChatId,
        firebaseChatId,
        chatType,
        (comments) => {
          // Update comments in real-time via dispatch
          dispatch(updateCommentsRealtime(comments));

          // Calculate unread count
          const state = getState();
          const currentUser = state.auth?.user;
          if (currentUser) {
            const { getUnreadComments } = require('../../utils/chatUtils');
            const unreadComments = getUnreadComments(comments, String(currentUser.user.id));
            dispatch(setCommentsUnreadCount(unreadComments.length));
          }
        },
        selectedPage?.id
      );

      // Store the unsubscribe function
      dispatch(setCommentsListener(result.unsubscribe));

      return result.comments;
    } catch (error) {
      console.error("Error loading chat comments:", error);
      return rejectWithValue(error.message || 'Failed to load comments');
    }
  }
);

export const sendCommentHybrid = createAsyncThunk(
  "metaBusinessSuite/sendCommentHybrid",
  async ({ backendChatId, firebaseChatId, chatType, text, author }, { getState, rejectWithValue }) => {
    try {
      const { sendComment } = await import('../../services/comments/hybridService');
      const { validateCommentText, validateAuthor } = await import('../../utils/commentValidation');

      // Get selectedPage from Redux state
      const state = getState().metaBusinessSuite;
      const selectedPage = state.selectedPage;

      // Validate inputs
      const textValidation = validateCommentText(text);
      if (!textValidation.isValid) {
        return rejectWithValue(`Invalid comment: ${textValidation.errors.join(', ')}`);
      }

      const authorValidation = validateAuthor(author);
      if (!authorValidation.isValid) {
        return rejectWithValue(`Invalid author: ${authorValidation.errors.join(', ')}`);
      }

      // Ensure pageId is a string
      const pageId = selectedPage?.id ? String(selectedPage.id) : null;

      const comment = await sendComment(backendChatId, firebaseChatId, chatType, text, author, pageId);
      return comment;
    } catch (error) {
      console.error("Error sending comment:", error);
      return rejectWithValue(error.message || 'Failed to send comment');
    }
  }
);

export const markCommentsAsReadHybrid = createAsyncThunk(
  "metaBusinessSuite/markCommentsAsReadHybrid",
  async ({ backendChatId, firebaseChatId, chatType, commentIds, user }, { getState, rejectWithValue }) => {
    try {
      console.log('markCommentsAsReadHybrid - Starting with:', {
        backendChatId,
        firebaseChatId,
        chatType,
        commentIds,
        user: user.name
      });

      // Get selectedPage from Redux state
      const state = getState().metaBusinessSuite;
      const selectedPage = state.selectedPage;

      const { markCommentsAsRead } = await import('../../services/comments/hybridService');

      await markCommentsAsRead(backendChatId, firebaseChatId, chatType, commentIds, user, selectedPage?.id);

      console.log('markCommentsAsReadHybrid - Successfully completed');
      return { commentIds, user };
    } catch (error) {
      console.error("Error marking comments as read:", error);
      return rejectWithValue(error.message || 'Failed to mark comments as read');
    }
  }
);

export const addComment = createAsyncThunk(
  "metaBusinessSuite/addComment",
  async ({ chatId, chatType, text, author }, { getState, rejectWithValue }) => {
    try {
      const { addComment: addCommentService } = await import('../../services/comments');
      const { validateCommentText, validateChatContext, validateAuthor } = await import('../../utils/commentValidation');

      // Get current user for permission validation
      const state = getState();
      const currentUser = state.auth?.user;
      const selectedPage = state.metaBusinessSuite?.selectedPage;

      // Validate inputs before making the request
      const chatValidation = validateChatContext(chatId, chatType);
      if (!chatValidation.isValid) {
        return rejectWithValue(`Invalid chat context: ${chatValidation.errors.join(', ')}`);
      }

      const textValidation = validateCommentText(text);
      if (!textValidation.isValid) {
        return rejectWithValue(`Invalid comment: ${textValidation.errors.join(', ')}`);
      }

      const authorValidation = validateAuthor(author);
      if (!authorValidation.isValid) {
        return rejectWithValue(`Invalid author: ${authorValidation.errors.join(', ')}`);
      }

      // Ensure pageId is a string
      const pageId = selectedPage?.id ? String(selectedPage.id) : null;

      const commentId = await addCommentService(chatId, chatType, text, author, currentUser, null, pageId);

      // Return the comment data that will be received via real-time listener
      return {
        id: commentId,
        text: textValidation.sanitizedText,
        author: authorValidation.sanitizedAuthor,
        createdAt: new Date().toISOString(),
        readBy: [],
        chatType
      };
    } catch (error) {
      console.error("Error adding comment:", error);

      // Use centralized error handling
      const { handleCommentError } = await import('../../utils/commentErrorHandling');
      const commentError = handleCommentError(error, 'add comment', {
        showToast: true, // Show toast for add comment errors
        throwError: false
      });

      return rejectWithValue(commentError.message);
    }
  }
);

export const listenToComments = createAsyncThunk(
  "metaBusinessSuite/listenToComments",
  async ({ chatId, chatType, currentUserId }, { dispatch, getState, rejectWithValue }) => {
    try {
      const { listenToComments: listenToCommentsService } = await import('../../services/comments');
      const { validateChatContext } = await import('../../utils/commentValidation');

      // Get current user for permission validation
      const state = getState();
      const currentUser = state.auth?.user;
      const selectedPage = state.metaBusinessSuite?.selectedPage;

      // Validate chat context before setting up listener
      const chatValidation = validateChatContext(chatId, chatType);
      if (!chatValidation.isValid) {
        return rejectWithValue(`Invalid chat context: ${chatValidation.errors.join(', ')}`);
      }

      // Clean up any existing listener first
      const metaState = getState().metaBusinessSuite;
      if (metaState.commentsListeners.unsubscribe) {
        metaState.commentsListeners.unsubscribe();
      }

      // Ensure pageId is a string
      const pageId = selectedPage?.id ? String(selectedPage.id) : null;

      const unsubscribe = listenToCommentsService(chatId, chatType, (comments, error) => {
        if (error) {
          console.error("Comments listener error:", error);
          dispatch(setCommentsLoading(false));

          // Use centralized error handling for listener errors
          import('../../utils/commentErrorHandling').then(({ handleCommentError }) => {
            handleCommentError(error, 'comments listener', {
              showToast: true,
              throwError: false
            });
          });
          return;
        }

        // Update comments in real-time
        dispatch(updateCommentsRealtime(comments));

        // Calculate unread count for current user
        if (currentUserId && comments.length > 0) {
          const unreadCount = comments.filter(comment => {
            const hasRead = comment.readBy?.some(receipt => receipt.userId === currentUserId);
            return !hasRead;
          }).length;

          dispatch(setCommentsUnreadCount(unreadCount));
        }

        dispatch(setCommentsLoading(false));
      }, currentUser, pageId);

      return unsubscribe;
    } catch (error) {
      console.error("Error setting up comments listener:", error);

      // Use centralized error handling
      const { handleCommentError } = await import('../../utils/commentErrorHandling');
      const commentError = handleCommentError(error, 'setup comments listener', {
        showToast: true,
        throwError: false
      });

      return rejectWithValue(commentError.message);
    }
  }
);

export const markCommentsAsReadAsync = createAsyncThunk(
  "metaBusinessSuite/markCommentsAsReadAsync",
  async ({ chatId, chatType, user, commentIds }, { dispatch, getState, rejectWithValue }) => {
    try {
      const { markCommentsAsRead } = await import('../../services/comments');
      const { validateChatContext } = await import('../../utils/commentValidation');

      // Get current user for permission validation
      const state = getState();
      const currentUser = state.auth?.user;
      const selectedPage = state.metaBusinessSuite?.selectedPage;

      // Validate chat context
      const chatValidation = validateChatContext(chatId, chatType);
      if (!chatValidation.isValid) {
        return rejectWithValue(`Invalid chat context: ${chatValidation.errors.join(', ')}`);
      }

      // Validate user information
      if (!user || !user.id || !user.name) {
        return rejectWithValue('Valid user information is required');
      }

      // Validate comment IDs
      if (!Array.isArray(commentIds) || commentIds.length === 0) {
        return rejectWithValue('Valid comment IDs are required');
      }

      // Ensure pageId is a string
      const pageId = selectedPage?.id ? String(selectedPage.id) : null;

      await markCommentsAsRead(chatId, chatType, commentIds, user, currentUser, pageId);

      // Update local state immediately after successful Firebase update
      dispatch(markCommentsAsRead({ userId: user.id, userName: user.name }));

      return { userId: user.id, userName: user.name };
    } catch (error) {
      console.error("Error marking comments as read:", error);

      // Use centralized error handling
      const { handleCommentError } = await import('../../utils/commentErrorHandling');
      const commentError = handleCommentError(error, 'mark comments as read', {
        showToast: true,
        throwError: false
      });

      return rejectWithValue(commentError.message);
    }
  }
);

// Start comments listener with automatic cleanup
export const startCommentsListener = createAsyncThunk(
  "metaBusinessSuite/startCommentsListener",
  async ({ chatId, chatType, currentUserId }, { dispatch, getState }) => {
    try {
      // Clean up any existing listener first
      dispatch(clearCommentsListener());

      // Start the new listener
      await dispatch(listenToComments({ chatId, chatType, currentUserId }));

      return { chatId, chatType };
    } catch (error) {
      console.error("Error starting comments listener:", error);
      throw error;
    }
  }
);

// Stop comments listener and cleanup
export const stopCommentsListener = createAsyncThunk(
  "metaBusinessSuite/stopCommentsListener",
  async (_, { dispatch }) => {
    try {
      dispatch(clearCommentsListener());
      dispatch(setComments([]));
      dispatch(setCommentsUnreadCount(0));
      return true;
    } catch (error) {
      console.error("Error stopping comments listener:", error);
      throw error;
    }
  }
);

// Enhanced Real-time Listeners using RealtimeManager
export const startRealtimeChatsListener = createAsyncThunk(
  "metaBusinessSuite/startRealtimeChatsListener",
  async ({ pageId }, { dispatch, getState }) => {
    try {
      const realtimeManager = (await import('../../services/firebase/realtimeManager')).default;

      // Clean up any existing listeners
      realtimeManager.cleanup(`all_chats_${pageId}`);

      const unsubscribe = realtimeManager.listenToAllChats(
        pageId,
        (chats, error) => {
          if (error) {
            console.error("Real-time chats listener error:", error);
            return;
          }

          // Update chats in Redux state
          dispatch(updateLatestMessages(chats));
        },
        {
          enablePerformanceMonitoring: true,
          batchUpdates: true
        }
      );

      // Store unsubscribe function
      dispatch(setLatestMessagesUnsubscribe(unsubscribe));

      return { pageId, listenerCount: realtimeManager.getActiveListenerCount() };
    } catch (error) {
      console.error("Error starting real-time chats listener:", error);
      throw error;
    }
  }
);

// New comprehensive page-level message listener
export const startPageMessageListener = createAsyncThunk(
  "metaBusinessSuite/startPageMessageListener",
  async ({ pageId }, { dispatch, getState }) => {
    try {
      const realtimeManager = (await import('../../services/firebase/realtimeManager')).default;

      console.log(`[PAGE_LISTENER] Starting comprehensive message listener for page ${pageId}`);

      // Clean up any existing page message listeners
      realtimeManager.cleanup(`page_messages_${pageId}`);

      const unsubscribe = realtimeManager.listenToAllPageMessages(
        pageId,
        // Callback for new messages
        (message, senderId, chatType, senderData) => {
          console.log(`[PAGE_LISTENER] New message detected:`, {
            messageId: message.id,
            senderId,
            chatType,
            message: message.message || message.text
          });

          // Update messages if this is for the currently selected chat
          const state = getState().metaBusinessSuite;
          const currentChatId = state.selectedChat?.id ||
            state.selectedWhatsappChat?.sender_phone_number?.toString().trim().replace(/^\+|\s+/g, "");

          if (senderId === currentChatId ||
            (chatType === 'whatsapp' && senderId === currentChatId)) {

            // Add the message to the current chat messages
            dispatch(updateMessages([message]));
            console.log(`[PAGE_LISTENER] Updated current chat messages for ${senderId}`);
          }
        },
        // Callback for latest messages updates
        (updateData) => {
          console.log(`[PAGE_LISTENER] Latest message update:`, updateData);

          // Create a chat object for the latest messages list
          const chatForLatestMessages = {
            id: updateData.senderId,
            sender: updateData.senderId,
            recipient: updateData.pageId,
            message: updateData.latestMessage.message || updateData.latestMessage.text,
            created_time: updateData.latestMessage.created_time,
            updated_time: updateData.latestMessage.created_time,
            type: updateData.latestMessage.type || 'text',
            page_id: updateData.pageId,
            chat_type: updateData.chatType,
            // Include any additional sender data
            ...updateData.senderData
          };

          // Update the latest messages list
          dispatch(updateLatestMessages([chatForLatestMessages]));
          console.log(`[PAGE_LISTENER] Updated latest messages for ${updateData.senderId}`);
        }
      );

      console.log(`[PAGE_LISTENER] Successfully started page message listener for ${pageId}`);
      return { success: true, unsubscribe, pageId };

    } catch (error) {
      console.error(`[PAGE_LISTENER] Failed to start page message listener for ${pageId}:`, error);
      throw error;
    }
  }
);

export const startRealtimeMessagesListener = createAsyncThunk(
  "metaBusinessSuite/startRealtimeMessagesListener",
  async ({ chatId, chatType, pageId }, { dispatch, getState }) => {
    try {
      const realtimeManager = (await import('../../services/firebase/realtimeManager')).default;

      // Clean up any existing listeners for this chat
      realtimeManager.cleanup(`messages_${pageId}_${chatId}`);

      const unsubscribe = realtimeManager.listenToMessages(
        chatId,
        chatType,
        pageId,
        (messages, error) => {
          if (error) {
            console.error("Real-time messages listener error:", error);
            return;
          }

          // Update messages in Redux state based on chat type
          if (chatType === 'whatsapp') {
            dispatch(updateWhatsappChatMessages(messages));
          } else {
            dispatch(updateMessages(messages));
          }
        },
        {
          enablePerformanceMonitoring: true,
          autoScrollToBottom: true
        }
      );

      // Store unsubscribe function
      dispatch(setMessagesSnapshotUnsubscribe(unsubscribe));

      return { chatId, chatType, pageId };
    } catch (error) {
      console.error("Error starting real-time messages listener:", error);
      throw error;
    }
  }
);

export const startRealtimeCommentsListener = createAsyncThunk(
  "metaBusinessSuite/startRealtimeCommentsListener",
  async ({ chatId, chatType, pageId, autoMarkAsRead = true, autoScrollToBottom = false }, { dispatch, getState }) => {
    try {
      const realtimeManager = (await import('../../services/firebase/realtimeManager')).default;
      const state = getState();
      const currentUser = state.auth?.user;

      if (!currentUser) {
        throw new Error("User must be authenticated to listen to comments");
      }

      // Clean up any existing listeners for this chat
      realtimeManager.cleanup(`comments_${pageId}_${chatId}`);

      const unsubscribe = realtimeManager.listenToComments(
        chatId,
        chatType,
        pageId,
        (comments, error) => {
          if (error) {
            console.error("Real-time comments listener error:", error);
            dispatch(setCommentsLoading(false));
            return;
          }

          // Update comments in Redux state
          dispatch(updateCommentsRealtime(comments));

          // Calculate unread count for current user
          if (currentUser) {
            const { getUnreadComments } = require('../../utils/chatUtils');
            const userInfo = {
              id: String(currentUser.user?.id || currentUser.id),
              name: currentUser.user?.name || currentUser.name
            };
            const unreadComments = getUnreadComments(comments, userInfo.id);
            dispatch(setCommentsUnreadCount(unreadComments.length));
          }

          dispatch(setCommentsLoading(false));
        },
        {
          id: String(currentUser.user?.id || currentUser.id),
          name: currentUser.user?.name || currentUser.name,
          photo: currentUser.user?.photo || currentUser.photo
        },
        {
          autoMarkAsRead,
          autoScrollToBottom,
          enablePerformanceMonitoring: true,
          batchReadReceipts: true
        }
      );

      // Store unsubscribe function
      dispatch(setCommentsListener(unsubscribe));

      return { chatId, chatType, pageId, autoMarkAsRead, autoScrollToBottom };
    } catch (error) {
      console.error("Error starting real-time comments listener:", error);
      throw error;
    }
  }
);

export const stopAllRealtimeListeners = createAsyncThunk(
  "metaBusinessSuite/stopAllRealtimeListeners",
  async (_, { dispatch }) => {
    try {
      const realtimeManager = (await import('../../services/firebase/realtimeManager')).default;

      // Get status before cleanup
      const status = realtimeManager.getStatus();
      console.log('[REALTIME] Stopping all listeners:', status);

      // Clean up all listeners
      realtimeManager.cleanupAll();

      // Clear Redux state
      dispatch(clearCommentsListener());
      dispatch(setLatestMessagesUnsubscribe(null));
      dispatch(setMessagesSnapshotUnsubscribe(null));

      return { cleanedUp: status.totalListeners };
    } catch (error) {
      console.error("Error stopping all real-time listeners:", error);
      throw error;
    }
  }
);

export const markCommentsAsReadRealtime = createAsyncThunk(
  "metaBusinessSuite/markCommentsAsReadRealtime",
  async ({ chatId, chatType, pageId, commentIds }, { getState }) => {
    try {
      const realtimeManager = (await import('../../services/firebase/realtimeManager')).default;
      const state = getState();
      const currentUser = state.auth?.user;

      if (!currentUser) {
        throw new Error("User must be authenticated to mark comments as read");
      }

      const userInfo = {
        id: String(currentUser.user?.id || currentUser.id),
        name: currentUser.user?.name || currentUser.name,
        photo: currentUser.user?.photo || currentUser.photo
      };

      await realtimeManager.markCommentsAsReadInFirebase(
        chatId,
        chatType,
        pageId,
        commentIds,
        userInfo
      );

      return { chatId, commentIds, user: userInfo };
    } catch (error) {
      console.error("Error marking comments as read in real-time:", error);
      throw error;
    }
  }
);

export const getRealtimeListenerStatus = createAsyncThunk(
  "metaBusinessSuite/getRealtimeListenerStatus",
  async () => {
    try {
      const realtimeManager = (await import('../../services/firebase/realtimeManager')).default;
      return realtimeManager.getStatus();
    } catch (error) {
      console.error("Error getting real-time listener status:", error);
      return { error: error.message };
    }
  }
);

// Initial state
const initialState = {
  // Chat state
  chats: [],
  selectedChat: null,
  messages: [],
  nestedMessages: [],
  latestMessages: null,

  // Page and filter state
  selectedPage: null,
  activeFilter: "messenger",
  messengerChats: [],
  instagramChats: [],

  // WhatsApp state
  whatsappChats: [],
  whatsappChatMessages: [],
  whatsAppAccounts: [],
  selectedWhatsAccount: null,
  selectedWhatsappChat: null,
  whatsAppAccountDetails: [],
  selectedPhone: null,
  businessPhoneNumbers: [],

  // Filter and pagination state
  startDate: "",
  endDate: "",
  filteredData: [],
  conversations: [],
  paginationMeta: [null, null],
  hasMore: true,

  // UI state
  disabledChat: false,
  disabledChatLimit: false,
  pageImg: null,
  loadingPagination: false,
  loadingChats: false,

  // Loading states for async operations
  loading: {
    fetchingLatestMessages: false,
    fetchingMessages: false,
    fetchingWhatsAppMessages: false,
    sendingMessage: false,
    sendingWhatsMessage: false,
    fetchingMoreChats: false,
  },

  // Error states
  error: {
    fetchLatestMessages: null,
    fetchMessages: null,
    fetchWhatsAppMessages: null,
    sendMessage: null,
    sendWhatsMessage: null,
    fetchMoreChats: null,
    fetchChats: null,
  },

  // Firestore listeners
  messagesSnapshotUnsubscribe: null,
  latestMessagesUnsubscribe: null,
  pollingInterval: null,

  // Comments system state
  comments: [],
  commentsModal: {
    isOpen: false,
    unreadCount: 0,
    loading: false,
    initialLoading: false, // Separate state for initial loading vs comment submission
  },
  commentsListeners: {
    unsubscribe: null,
  },
  // Track unread comments per chat
  chatUnreadComments: {}, // { chatId: { count: number, lastReadAt: timestamp } }
  // Track unread messages per chat
  chatUnreadMessages: {}, // { chatId: { count: number, lastReadAt: timestamp } }
  shouldPlayNotificationSound: false,
  lastNotificationTimestamp: 0, // Prevent notification spam
  shouldRefreshComments: false,
};

// Create slice
const metaBusinessSuiteSlice = createSlice({
  name: "metaBusinessSuite",
  initialState,
  reducers: {
    // Basic setters
    setChats: (state, action) => {
      state.chats = action.payload;
    },
    setSelectedChat: (state, action) => {
      state.selectedChat = action.payload;
      // Clear comments when switching chats
      if (state.selectedChat !== action.payload) {
        state.comments = [];
        state.commentsModal.unreadCount = 0;
        state.commentsModal.isOpen = false;
        // Clear any existing listener
        if (state.commentsListeners.unsubscribe) {
          state.commentsListeners.unsubscribe();
          state.commentsListeners.unsubscribe = null;
        }
      }
    },
    setMessages: (state, action) => {
      state.messages = action.payload;
    },
    setNestedMessages: (state, action) => {
      state.nestedMessages = action.payload;
    },
    setSelectedPage: (state, action) => {
      state.selectedPage = action.payload;
    },
    setLatestMessages: (state, action) => {
      state.latestMessages = action.payload;
    },
    setActiveFilter: (state, action) => {
      // Clear comments when switching between chat types (WhatsApp vs Messenger/Instagram)
      if (state.activeFilter !== action.payload) {
        state.comments = [];
        state.commentsModal.unreadCount = 0;
        state.commentsModal.isOpen = false;
        // Clear any existing listener
        if (state.commentsListeners.unsubscribe) {
          state.commentsListeners.unsubscribe();
          state.commentsListeners.unsubscribe = null;
        }
      }
      state.activeFilter = action.payload;
    },
    setMessengerChats: (state, action) => {
      state.messengerChats = action.payload;
    },
    setInstagramChats: (state, action) => {
      state.instagramChats = action.payload;
    },
    setWhatsappChats: (state, action) => {
      state.whatsappChats = action.payload;
    },
    setWhatsappChatMessages: (state, action) => {
      state.whatsappChatMessages = action.payload;
    },
    setWhatsAppAccounts: (state, action) => {
      state.whatsAppAccounts = action.payload;
    },
    setSelectedWhatsAccount: (state, action) => {
      state.selectedWhatsAccount = action.payload;
    },
    setSelectedWhatsappChat: (state, action) => {
      state.selectedWhatsappChat = action.payload;
      // Clear comments when switching chats
      if (state.selectedWhatsappChat !== action.payload) {
        state.comments = [];
        state.commentsModal.unreadCount = 0;
        state.commentsModal.isOpen = false;
        // Clear any existing listener
        if (state.commentsListeners.unsubscribe) {
          state.commentsListeners.unsubscribe();
          state.commentsListeners.unsubscribe = null;
        }
      }
    },
    setWhatsAppAccountDetails: (state, action) => {
      state.whatsAppAccountDetails = action.payload;
    },
    setSelectedPhone: (state, action) => {
      state.selectedPhone = action.payload;
    },
    setStartDate: (state, action) => {
      state.startDate = action.payload;
    },
    setEndDate: (state, action) => {
      state.endDate = action.payload;
    },
    setFilteredData: (state, action) => {
      state.filteredData = action.payload;
    },
    setConversations: (state, action) => {
      state.conversations = action.payload;
    },
    setBusinessPhoneNumbers: (state, action) => {
      state.businessPhoneNumbers = action.payload;
    },
    setDisabledChat: (state, action) => {
      state.disabledChat = action.payload;
    },
    setDisabledChatLimit: (state, action) => {
      state.disabledChatLimit = action.payload;
    },
    setPageImg: (state, action) => {
      state.pageImg = action.payload;
    },
    setPaginationMeta: (state, action) => {
      state.paginationMeta = action.payload;
    },
    setHasMore: (state, action) => {
      state.hasMore = action.payload;
    },
    setLoadingPagination: (state, action) => {
      state.loadingPagination = action.payload;
    },
    setLoadingChats: (state, action) => {
      state.loadingChats = action.payload;
    },
    setMessagesSnapshotUnsubscribe: (state, action) => {
      state.messagesSnapshotUnsubscribe = action.payload;
    },
    setLatestMessagesUnsubscribe: (state, action) => {
      state.latestMessagesUnsubscribe = action.payload;
    },
    setPollingInterval: (state, action) => {
      state.pollingInterval = action.payload;
    },
    selectChat: (state, action) => {
      const thread = action.payload;
      state.selectedChat = thread;
      state.messages = [];
      state.nestedMessages = [];
      state.disabledChat = false;

      // Clean up previous listener
      if (state.messagesSnapshotUnsubscribe) {
        state.messagesSnapshotUnsubscribe();
        state.nestedMessages = [];
        state.messagesSnapshotUnsubscribe = null;
      }
    },
    selectWhatsappChat: (state, action) => {
      const thread = action.payload;
      state.selectedChat = thread;
      state.whatsappChatMessages = [];
      state.nestedMessages = [];
      state.disabledChat = false;
      state.selectedWhatsappChat = thread;

      // Clean up previous listener
      if (state.messagesSnapshotUnsubscribe) {
        state.messagesSnapshotUnsubscribe();
        state.nestedMessages = [];
        state.messagesSnapshotUnsubscribe = null;
      }
    },
    updateWhatsappChatsWithFirebase: (state, action) => {
      state.whatsappChats = action.payload(state.whatsappChats);
    },
    handleSelectWhatsAppAccount: (state, action) => {
      const { whatsAppAccount, phone } = action.payload;
      state.selectedWhatsAccount = whatsAppAccount;
      state.selectedPhone = phone;
      state.businessPhoneNumbers = whatsAppAccount?.phone_numbers?.data?.map((account) => ({
        ...account,
        phone: parsePhoneNumber(account?.display_phone_number),
      })) || [];
    },
    resetFilteredChatsPages: (state) => {
      state.nestedMessages = [];
      state.messages = [];
    },
    // Debug function to help diagnose message flow issues
    debugMessageFlow: (state, action) => {
      const { selectedChat, latestMessages, messages } = state;
      const debugInfo = {
        timestamp: new Date().toISOString(),
        selectedChat: selectedChat ? {
          id: selectedChat.id,
          type: selectedChat.sender_phone_number ? 'whatsapp' :
            selectedChat.flage === 'instagram' ? 'instagram' : 'messenger',
          participants: selectedChat.participants?.data?.map(p => p.id)
        } : null,
        latestMessagesCount: latestMessages?.length || 0,
        messagesCount: messages?.length || 0,
        latestMessagesForChat: latestMessages?.filter(msg =>
          selectedChat && (
            msg.id === selectedChat.id ||
            msg.chat_id === selectedChat.id ||
            msg.sender === selectedChat.participants?.data?.[0]?.id ||
            msg.recipient === selectedChat.participants?.data?.[0]?.id
          )
        ) || [],
        action: action.payload
      };

      console.log('[DEBUG_MESSAGE_FLOW]', debugInfo);

      // Store debug info in state for inspection
      state.debugInfo = debugInfo;
    },
    updateMessages: (state, action) => {
      const newMessages = action.payload;
      const uniqueMessages = [...state.messages];

      newMessages.forEach(message => {
        if (!uniqueMessages.some((m) => m.id === message.id)) {
          uniqueMessages.push(message);
        }
      });

      uniqueMessages.sort(
        (a, b) => parseDate(a.created_time) - parseDate(b.created_time),
      );
      state.messages = uniqueMessages;

      // For incoming messages, we need to update the Firebase parent document
      // so that the latest messages listener can pick up the changes
      if (state.selectedChat && newMessages.length > 0) {
        const latestMessage = newMessages[newMessages.length - 1]; // Get the most recent message

        // Use the new chat ID-based approach for consistency
        const chatType = state.selectedChat?.sender_phone_number ? 'whatsapp' :
          state.selectedChat?.flage === 'instagram' ? 'instagram' : 'messenger';

        let chatId;
        if (chatType === 'whatsapp') {
          chatId = state.selectedChat.sender_phone_number?.toString().trim().replace(/^\+|\s+|-/g, "");
        } else {
          chatId = state.selectedChat.id;
        }

        if (chatId && state.latestMessages) {
          // Update the latestMessages array using the consistent chat ID
          const updatedLatestMessages = state.latestMessages.map(msg => {
            // Check if this message belongs to the current chat using chat ID
            if (msg.id?.toString() === chatId?.toString() ||
              msg.sender?.toString() === chatId?.toString() ||
              msg.recipient?.toString() === chatId?.toString()) {
              return {
                ...msg,
                message: latestMessage.message,
                created_time: latestMessage.created_time,
                updated_time: latestMessage.created_time,
              };
            }
            return msg;
          });

          // If no existing message found, add a new one using chat ID
          const existingMessage = updatedLatestMessages.find(msg =>
            msg.id?.toString() === chatId?.toString() ||
            msg.sender?.toString() === chatId?.toString() ||
            msg.recipient?.toString() === chatId?.toString()
          );

          if (!existingMessage) {
            // Create new latest message entry using chat ID for consistency
            const participantIndex = state.selectedChat?.flage === 'instagram' ? 1 : 0;
            const participantId = state.selectedChat?.participants?.data[participantIndex]?.id;

            updatedLatestMessages.push({
              id: chatId, // Use chat ID as the identifier
              sender: latestMessage.sender || participantId,
              recipient: latestMessage.recipient || state.selectedChat?.participants?.data[participantIndex === 1 ? 0 : 1]?.id,
              message: latestMessage.message,
              created_time: latestMessage.created_time,
              updated_time: latestMessage.created_time,
              page_id: state.selectedPage?.id // Maintain page association
            });
          }

          state.latestMessages = updatedLatestMessages;
        }
      }
    },
    updateWhatsappChatMessages: (state, action) => {
      const newMessages = action.payload;

      // Ensure newMessages is an array
      if (!Array.isArray(newMessages)) {
        return;
      }

      const uniqueMessages = [...state.whatsappChatMessages];

      newMessages.forEach(newMsg => {
        const exists = uniqueMessages.some(msg =>
          msg.id === newMsg.id ||
          (msg.message_id && msg.message_id === newMsg.message_id) ||
          (msg.message === newMsg.message &&
            msg.sender === newMsg.sender &&
            msg.created_time === newMsg.created_time)
        );

        if (!exists) {
          console.log("Adding new message to chat:", newMsg);
          uniqueMessages.push(newMsg);
        } else {
          console.log("Message already exists, skipping:", newMsg.id);
        }
      });

      // Sort messages by creation time (ascending order for display)
      const sortedMessages = uniqueMessages.sort((a, b) => {
        const dateA = parseDate(a.created_time);
        const dateB = parseDate(b.created_time);
        return dateA - dateB;
      });

      state.whatsappChatMessages = sortedMessages;

      // Also update whatsappChats if we have a selected WhatsApp chat and new messages
      if (state.selectedWhatsappChat && newMessages.length > 0) {
        // Find the latest message from the new messages
        const latestMessage = newMessages.sort((a, b) => {
          const dateA = parseDate(a.created_time);
          const dateB = parseDate(b.created_time);
          return dateB - dateA; // Sort in descending order to get the latest
        })[0];

        // Update the selected WhatsApp chat with the latest message
        const updatedWhatsappChats = state.whatsappChats.map(chat => {
          // Match by sender_phone_number to ensure we update the correct chat
          if (chat.sender_phone_number === state.selectedWhatsappChat.sender_phone_number) {
            return {
              ...chat,
              last_message: {
                ...chat.last_message,
                message: latestMessage.message,
                from: latestMessage.sender,
                type: latestMessage.type || "text",
                message_id: latestMessage.message_id,
                timestamp: Math.floor(new Date(latestMessage.created_time).getTime() / 1000).toString(),
                created_at: latestMessage.created_time,
                updated_at: latestMessage.created_time,
                url: latestMessage.url || null
              },
              updated_time: latestMessage.created_time,
              created_time: latestMessage.created_time,
            };
          }
          return chat;
        });

        // Sort by latest message time
        const sortedChats = updatedWhatsappChats.sort((a, b) => {
          const timeA = a.last_message?.created_at
            ? parseDate(a.last_message.created_at)
            : a.updated_at
              ? parseDate(a.updated_at)
              : parseDate(a.created_at);

          const timeB = b.last_message?.created_at
            ? parseDate(b.last_message.created_at)
            : b.updated_at
              ? parseDate(b.updated_at)
              : parseDate(b.created_at);

          return timeB - timeA; // Sort in descending order (newest first)
        });

        state.whatsappChats = sortedChats;
      }

      // Also update latestMessages for the merged selector
      if (newMessages.length > 0 && state.selectedWhatsappChat) {
        // Find the latest message from the new messages
        const latestMessage = newMessages.sort((a, b) => {
          const dateA = parseDate(a.created_time);
          const dateB = parseDate(b.created_time);
          return dateB - dateA; // Sort in descending order to get the latest
        })[0];

        // Update latestMessages to include the latest WhatsApp message
        if (state.latestMessages) {
          const updatedLatestMessages = state.latestMessages.map(msg => {
            // Match by phone numbers - check if this message is for the current chat
            const msgSender = msg.sender?.toString();
            const msgRecipient = msg.recipient?.toString();
            const currentSender = state.selectedWhatsappChat.sender_phone_number?.toString();
            const currentRecipient = state.selectedPhone?.display_phone_number?.trim().replace(/^\+|\s+/g, '');

            if ((msgSender === currentSender && msgRecipient === currentRecipient) ||
              (msgSender === currentRecipient && msgRecipient === currentSender)) {
              return {
                ...msg,
                message: latestMessage.message,
                message_id: latestMessage.message_id,
                created_time: latestMessage.created_time,
                updated_time: latestMessage.created_time,
                created_at: latestMessage.created_time,
                url: latestMessage.url || null,
                type: latestMessage.type || "text"
              };
            }
            return msg;
          });

          // If no existing message was updated, add a new one
          const messageExists = updatedLatestMessages.some(msg => {
            const msgSender = msg.sender?.toString();
            const msgRecipient = msg.recipient?.toString();
            const currentSender = state.selectedWhatsappChat.sender_phone_number?.toString();
            const currentRecipient = state.selectedPhone?.display_phone_number?.trim().replace(/^\+|\s+/g, '');

            return (msgSender === currentSender && msgRecipient === currentRecipient) ||
              (msgSender === currentRecipient && msgRecipient === currentSender);
          });

          if (!messageExists) {
            updatedLatestMessages.push({
              id: state.selectedWhatsappChat.sender_phone_number,
              sender: latestMessage.sender,
              recipient: latestMessage.recipient,
              message: latestMessage.message,
              message_id: latestMessage.message_id,
              created_time: latestMessage.created_time,
              updated_time: latestMessage.created_time,
              created_at: latestMessage.created_time,
              type: latestMessage.type || "text",
              url: latestMessage.url || null
            });
          }

          state.latestMessages = updatedLatestMessages.sort((a, b) =>
            parseDate(b.created_time) - parseDate(a.created_time)
          );
        }
      }
    },
    clearErrors: (state) => {
      state.error = {
        fetchLatestMessages: null,
        fetchMessages: null,
        fetchWhatsAppMessages: null,
        sendMessage: null,
        sendWhatsMessage: null,
        fetchMoreChats: null,
        fetchChats: null,
      };
    },
    updateChats: (state, action) => {
      state.chats = action.payload(state.chats);
    },
    updateLatestMessages: (state, action) => {
      state.latestMessages = action.payload(state.latestMessages);
    },
    updateInstagramChats: (state, action) => {
      state.instagramChats = action.payload(state.instagramChats);
    },
    updateMessengerChats: (state, action) => {
      state.messengerChats = action.payload(state.messengerChats);
    },
    updateSelectedWhatsappChat: (state, action) => {
      state.selectedWhatsappChat = action.payload(state.selectedWhatsappChat);
    },

    // Comments system actions
    setComments: (state, action) => {
      state.comments = action.payload;
    },
    addCommentToState: (state, action) => {
      state.comments.push(action.payload);
    },
    updateComment: (state, action) => {
      const { commentId, updates } = action.payload;
      const commentIndex = state.comments.findIndex(comment => comment.id === commentId);
      if (commentIndex !== -1) {
        state.comments[commentIndex] = { ...state.comments[commentIndex], ...updates };
      }
    },
    removeComment: (state, action) => {
      state.comments = state.comments.filter(comment => comment.id !== action.payload);
    },
    setCommentsModal: (state, action) => {
      state.commentsModal = { ...state.commentsModal, ...action.payload };
    },
    openCommentsModal: (state) => {
      state.commentsModal.isOpen = true;
    },
    closeCommentsModal: (state) => {
      state.commentsModal.isOpen = false;
    },
    setCommentsUnreadCount: (state, action) => {
      state.commentsModal.unreadCount = action.payload;
    },
    incrementCommentsUnreadCount: (state) => {
      state.commentsModal.unreadCount += 1;
    },
    resetCommentsUnreadCount: (state) => {
      state.commentsModal.unreadCount = 0;

      // Also reset chat-specific unread count
      const currentChatId = state.selectedChat?.id || state.selectedWhatsappChat?.id;
      if (currentChatId && state.chatUnreadComments[currentChatId]) {
        state.chatUnreadComments[currentChatId].count = 0;
        state.chatUnreadComments[currentChatId].lastReadAt = new Date().toISOString();
      }
    },
    setCommentsLoading: (state, action) => {
      state.commentsModal.loading = action.payload;
    },
    setCommentsInitialLoading: (state, action) => {
      state.commentsModal.initialLoading = action.payload;
    },
    setCommentsListener: (state, action) => {
      state.commentsListeners.unsubscribe = action.payload;
    },
    clearCommentsListener: (state) => {
      if (state.commentsListeners.unsubscribe) {
        state.commentsListeners.unsubscribe();
        state.commentsListeners.unsubscribe = null;
      }
    },
    updateCommentsRealtime: (state, action) => {
      const newComments = action.payload;
      console.log('Redux updateCommentsRealtime called with:', newComments);

      // Check if there are new comments from OTHER users (for sound notification)
      const previousCommentIds = new Set(state.comments.map(c => c.id));
      const currentUserId = state.auth?.user?.user?.id ? String(state.auth.user.user.id) : null;

      const newCommentsFromOthers = newComments.filter(comment => {
        // Only consider comments that are new
        if (previousCommentIds.has(comment.id)) return false;

        // Only consider comments from other users (not current user)
        if (currentUserId && comment.author?.id === currentUserId) {
          console.log('[NOTIFICATION] Skipping own comment:', comment.id);
          return false;
        }

        console.log('[NOTIFICATION] New comment from other user:', comment.id, 'by', comment.author?.name);
        return true;
      });

      const hasNewCommentsFromOthers = newCommentsFromOthers.length > 0;

      // Replace existing comments with new ones from real-time updates
      state.comments = newComments.sort((a, b) => {
        const dateA = new Date(a.createdAt);
        const dateB = new Date(b.createdAt);
        return dateA - dateB;
      });
      console.log('Redux state.comments updated to:', state.comments);

      // Calculate unread count for current user
      if (currentUserId && !state.commentsModal.isOpen) {
        const unreadCount = state.comments.filter(comment => {
          // Don't count own comments as unread
          if (comment.author?.id === currentUserId) return false;

          // Check if user has read this comment
          const hasRead = comment.readBy?.some(receipt => receipt.userId === currentUserId);
          return !hasRead;
        }).length;

        state.commentsModal.unreadCount = unreadCount;
        console.log('Updated unread count to:', unreadCount);

        // Update chat-specific unread count
        const currentChatId = state.selectedChat?.id || state.selectedWhatsappChat?.id;
        if (currentChatId) {
          if (!state.chatUnreadComments[currentChatId]) {
            state.chatUnreadComments[currentChatId] = { count: 0, lastReadAt: null };
          }
          state.chatUnreadComments[currentChatId].count = unreadCount;
        }
      }

      // Trigger sound notification ONLY for new comments from OTHER users
      if (hasNewCommentsFromOthers) {
        const currentChatId = state.selectedChat?.id || state.selectedWhatsappChat?.id;
        const isCommentsModalOpen = state.commentsModal.isOpen;
        const now = Date.now();
        const timeSinceLastNotification = now - state.lastNotificationTimestamp;
        const minNotificationInterval = 2000; // 2 seconds minimum between notifications

        // Only play sound if:
        // 1. There are new comments from other users AND
        // 2. User is in the chat where the comment was added AND
        // 3. Comments modal is NOT open (to avoid spam when actively viewing) AND
        // 4. At least 2 seconds have passed since last notification (prevent spam)
        if (currentChatId && !isCommentsModalOpen && timeSinceLastNotification > minNotificationInterval) {
          state.shouldPlayNotificationSound = true;
          state.lastNotificationTimestamp = now;
          console.log('[NOTIFICATION] Playing sound for new comment from another user');
        } else if (timeSinceLastNotification <= minNotificationInterval) {
          console.log('[NOTIFICATION] Skipping sound - too soon since last notification');
        }
      }

      // Update loading state
      state.commentsModal.loading = false;
    },
    markCommentsAsRead: (state, action) => {
      const { userId, userName } = action.payload;
      const currentTime = new Date().toISOString();

      state.comments = state.comments.map(comment => {
        // Check if user has already read this comment
        const hasRead = comment.readBy?.some(receipt => receipt.userId === userId);

        if (!hasRead) {
          return {
            ...comment,
            readBy: [
              ...(comment.readBy || []),
              {
                userId,
                userName,
                readAt: currentTime
              }
            ]
          };
        }
        return comment;
      });

      // Reset unread count when marking as read
      state.commentsModal.unreadCount = 0;

      // Update chat-specific unread count
      const currentChatId = state.selectedChat?.id || state.selectedWhatsappChat?.id;
      if (currentChatId && state.chatUnreadComments[currentChatId]) {
        state.chatUnreadComments[currentChatId].count = 0;
        state.chatUnreadComments[currentChatId].lastReadAt = new Date().toISOString();
      }
    },
    resetNotificationSound: (state) => {
      state.shouldPlayNotificationSound = false;
      // Don't reset timestamp here - let it naturally expire to prevent spam
    },
    resetRefreshCommentsFlag: (state) => {
      state.shouldRefreshComments = false;
    },
    updateUnreadCommentCounts: (state, action) => {
      const { currentUserId } = action.payload;

      if (currentUserId) {
        const unreadCount = state.comments.filter(comment => {
          // Don't count own comments as unread
          if (comment.author?.id === currentUserId) return false;

          // Check if current user has read this comment
          const hasRead = comment.readBy?.some(receipt => receipt.userId === currentUserId);
          return !hasRead;
        }).length;

        state.commentsModal.unreadCount = unreadCount;

        // Update chat-specific unread count
        const currentChatId = state.selectedChat?.id || state.selectedWhatsappChat?.id;
        if (currentChatId) {
          if (!state.chatUnreadComments[currentChatId]) {
            state.chatUnreadComments[currentChatId] = { count: 0, lastReadAt: null };
          }
          state.chatUnreadComments[currentChatId].count = unreadCount;
        }
      }
    },
    updateUnreadMessageCounts: (state, action) => {
      const { chatId, unreadCount } = action.payload;

      if (chatId) {
        if (!state.chatUnreadMessages[chatId]) {
          state.chatUnreadMessages[chatId] = { count: 0, lastReadAt: null };
        }
        state.chatUnreadMessages[chatId].count = unreadCount;
        state.chatUnreadMessages[chatId].lastReadAt = new Date().toISOString();
      }
    },
    markMessagesAsRead: (state, action) => {
      const { chatId } = action.payload;

      if (chatId && state.chatUnreadMessages[chatId]) {
        state.chatUnreadMessages[chatId].count = 0;
        state.chatUnreadMessages[chatId].lastReadAt = new Date().toISOString();
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Latest Messages
      .addCase(fetchLatestMessages.pending, (state) => {
        state.loading.fetchingLatestMessages = true;
        state.error.fetchLatestMessages = null;
      })
      .addCase(fetchLatestMessages.fulfilled, (state, action) => {
        state.loading.fetchingLatestMessages = false;
        state.messagesSnapshotUnsubscribe = action.payload.unsubscribe;
      })
      .addCase(fetchLatestMessages.rejected, (state, action) => {
        state.loading.fetchingLatestMessages = false;
        state.error.fetchLatestMessages = action.payload;
      })

      // Fetch Messages
      .addCase(fetchMessages.pending, (state) => {
        state.loading.fetchingMessages = true;
        state.error.fetchMessages = null;
        state.nestedMessages = [];
        state.messages = [];
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.loading.fetchingMessages = false;
        state.nestedMessages = action.payload.nestedMessages;
        state.messages = action.payload.messages;
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.loading.fetchingMessages = false;
        state.error.fetchMessages = action.payload;
      })

      // Fetch WhatsApp Messages
      .addCase(fetchWhatsAppMessages.pending, (state) => {
        state.loading.fetchingWhatsAppMessages = true;
        state.error.fetchWhatsAppMessages = null;
      })
      .addCase(fetchWhatsAppMessages.fulfilled, (state, action) => {
        state.loading.fetchingWhatsAppMessages = false;

        // The messages are already set in the thunk, just update the chat and unsubscribe
        const { updatedChat, unsubscribe } = action.payload;

        state.selectedWhatsappChat = updatedChat;
        state.messagesSnapshotUnsubscribe = unsubscribe;
      })
      .addCase(fetchWhatsAppMessages.rejected, (state, action) => {
        state.loading.fetchingWhatsAppMessages = false;
        state.error.fetchWhatsAppMessages = action.payload;
      })

      // Fetch WhatsApp Latest Messages
      .addCase(fetchWhatsAppLatestMessages.fulfilled, (state, action) => {
        if (action.payload) {
          const { firebaseChats, unsubscribe } = action.payload;
          state.whatsappChats = firebaseChats;
          state.messagesSnapshotUnsubscribe = unsubscribe;
        }
      })

      // Fetch WhatsApp Chats From API
      .addCase(fetchWhatsAppChatsFromAPI.fulfilled, (state, action) => {
        // API chats synced to Firebase successfully
      })

      // Select Chat Async
      .addCase(selectChatAsync.fulfilled, (state, action) => {
        state.selectedChat = action.payload;
      })

      // Select WhatsApp Chat Async
      .addCase(selectWhatsappChatAsync.fulfilled, (state, action) => {
        state.selectedWhatsappChat = action.payload;
      })

      // Send Message
      .addCase(sendMessage.pending, (state) => {
        state.loading.sendingMessage = true;
        state.error.sendMessage = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.loading.sendingMessage = false;
        // The payload is now a boolean (true/false) indicating success/failure
        if (!action.payload) {
          state.error.sendMessage = "Failed to send message";
        }
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.loading.sendingMessage = false;
        state.error.sendMessage = action.payload || "Failed to send message";
      })

      // Send WhatsApp Message
      .addCase(sendWhatsMessage.pending, (state) => {
        state.loading.sendingWhatsMessage = true;
        state.error.sendWhatsMessage = null;
      })
      .addCase(sendWhatsMessage.fulfilled, (state, action) => {
        state.loading.sendingWhatsMessage = false;
      })
      .addCase(sendWhatsMessage.rejected, (state, action) => {
        state.loading.sendingWhatsMessage = false;
        state.error.sendWhatsMessage = action.payload || "Failed to send WhatsApp message";
      })

      // Listen to All WhatsApp Messages
      .addCase(listenToAllWhatsappMessages.fulfilled, (state, action) => {
        // Handle the logic for listening to all WhatsApp messages
      })

      // Comments system async thunks
      .addCase(fetchComments.pending, (state) => {
        state.commentsModal.loading = true;
      })
      .addCase(fetchComments.fulfilled, (state, action) => {
        state.commentsModal.loading = false;
        state.comments = action.payload;
      })
      .addCase(fetchComments.rejected, (state, action) => {
        state.commentsModal.loading = false;
        console.error("Failed to fetch comments:", action.payload);
      })

      .addCase(addComment.pending, (state) => {
        state.commentsModal.loading = true;
      })
      .addCase(addComment.fulfilled, (state, action) => {
        state.commentsModal.loading = false;
        state.comments.push(action.payload);

        // Calculate unread count for current user (if modal is closed)
        const currentUserId = state.auth?.user?.user?.id ? String(state.auth.user.user.id) : null;
        if (currentUserId && !state.commentsModal.isOpen) {
          const unreadCount = state.comments.filter(comment => {
            // Don't count own comments as unread
            if (comment.author?.id === currentUserId) return false;

            // Check if user has read this comment
            const hasRead = comment.readBy?.some(receipt => receipt.userId === currentUserId);
            return !hasRead;
          }).length;

          state.commentsModal.unreadCount = unreadCount;
          console.log('Updated unread count after adding comment:', unreadCount);

          // Update chat-specific unread count
          const currentChatId = state.selectedChat?.id || state.selectedWhatsappChat?.id;
          if (currentChatId) {
            if (!state.chatUnreadComments[currentChatId]) {
              state.chatUnreadComments[currentChatId] = { count: 0, lastReadAt: null };
            }
            state.chatUnreadComments[currentChatId].count = unreadCount;
          }
        }
      })
      .addCase(addComment.rejected, (state, action) => {
        state.commentsModal.loading = false;
        console.error("Failed to add comment:", action.payload);
      })

      .addCase(listenToComments.fulfilled, (state, action) => {
        state.commentsListeners.unsubscribe = action.payload;
      })
      .addCase(listenToComments.rejected, (state, action) => {
        console.error("Failed to set up comments listener:", action.payload);
      })

      .addCase(markCommentsAsReadAsync.fulfilled, (state, action) => {
        // The markCommentsAsRead reducer will handle the state update
        // This is just for handling the async completion
      })
      .addCase(markCommentsAsReadAsync.rejected, (state, action) => {
        console.error("Failed to mark comments as read:", action.payload);
      })

      // Start comments listener
      .addCase(startCommentsListener.pending, (state) => {
        state.commentsModal.loading = true;
      })
      .addCase(startCommentsListener.fulfilled, (state, action) => {
        state.commentsModal.loading = false;
      })
      .addCase(startCommentsListener.rejected, (state, action) => {
        state.commentsModal.loading = false;
        console.error("Failed to start comments listener:", action.payload);
      })

      // Stop comments listener
      .addCase(stopCommentsListener.fulfilled, (state) => {
        state.commentsModal.loading = false;
      })
      .addCase(stopCommentsListener.rejected, (state, action) => {
        console.error("Failed to stop comments listener:", action.payload);
      })

      // Hybrid Comments Actions
      .addCase(loadChatCommentsHybrid.pending, (state) => {
        state.commentsModal.initialLoading = true; // Use initial loading for skeleton
        state.commentsModal.loading = false; // Don't block comment input
        state.comments = []; // Clear existing comments
      })
      .addCase(loadChatCommentsHybrid.fulfilled, (state, action) => {
        state.commentsModal.initialLoading = false;
        state.commentsModal.loading = false;
        state.comments = action.payload;
      })
      .addCase(loadChatCommentsHybrid.rejected, (state, action) => {
        state.commentsModal.initialLoading = false;
        state.commentsModal.loading = false;
        console.error("Failed to load chat comments:", action.payload);
      })

      .addCase(sendCommentHybrid.pending, (state) => {
        state.commentsModal.loading = true;
      })
      .addCase(sendCommentHybrid.fulfilled, (state, action) => {
        state.commentsModal.loading = false;
        // Comment will be added via real-time listener
      })
      .addCase(sendCommentHybrid.rejected, (state, action) => {
        state.commentsModal.loading = false;
        console.error("Failed to send comment:", action.payload);
      })

      .addCase(markCommentsAsReadHybrid.fulfilled, (state, action) => {
        // Update local state immediately with read receipts
        const { commentIds, user } = action.payload;
        const currentTime = new Date().toISOString();

        state.comments = state.comments.map(comment => {
          if (commentIds.includes(comment.id)) {
            // Check if user has already read this comment
            const hasRead = comment.readBy?.some(receipt => receipt.userId === user.id);

            if (!hasRead) {
              return {
                ...comment,
                readBy: [
                  ...(comment.readBy || []),
                  {
                    userId: user.id,
                    userName: user.name,
                    userPhoto: user.photo || null,
                    readAt: currentTime
                  }
                ]
              };
            }
          }
          return comment;
        });

        console.log('Updated local state with read receipts for user:', user.name);

        // Note: Real-time sync will handle updating read receipts for other users
        // No need to force refresh as it causes infinite loops
      })
      .addCase(markCommentsAsReadHybrid.rejected, (state, action) => {
        console.error("Failed to mark comments as read:", action.payload);
      })

      // Enhanced Real-time Listeners
      .addCase(startRealtimeChatsListener.pending, (state) => {
        state.loading.fetchingLatestMessages = true;
      })
      .addCase(startRealtimeChatsListener.fulfilled, (state, action) => {
        state.loading.fetchingLatestMessages = false;
        console.log(`[REALTIME] Started chats listener for page ${action.payload.pageId}, active listeners: ${action.payload.listenerCount}`);
      })
      .addCase(startRealtimeChatsListener.rejected, (state, action) => {
        state.loading.fetchingLatestMessages = false;
        console.error("Failed to start real-time chats listener:", action.payload);
      })

      .addCase(startRealtimeMessagesListener.pending, (state) => {
        state.loading.fetchingMessages = true;
      })
      .addCase(startRealtimeMessagesListener.fulfilled, (state, action) => {
        state.loading.fetchingMessages = false;
        console.log(`[REALTIME] Started messages listener for chat ${action.payload.chatId}`);
      })
      .addCase(startRealtimeMessagesListener.rejected, (state, action) => {
        state.loading.fetchingMessages = false;
        console.error("Failed to start real-time messages listener:", action.payload);
      })

      .addCase(startRealtimeCommentsListener.pending, (state) => {
        state.commentsModal.loading = true;
      })
      .addCase(startRealtimeCommentsListener.fulfilled, (state, action) => {
        state.commentsModal.loading = false;
        console.log(`[REALTIME] Started comments listener for chat ${action.payload.chatId}, auto-mark-as-read: ${action.payload.autoMarkAsRead}`);
      })
      .addCase(startRealtimeCommentsListener.rejected, (state, action) => {
        state.commentsModal.loading = false;
        console.error("Failed to start real-time comments listener:", action.payload);
      })

      .addCase(stopAllRealtimeListeners.fulfilled, (state, action) => {
        console.log(`[REALTIME] Stopped ${action.payload.cleanedUp} listeners`);
        state.latestMessagesUnsubscribe = null;
        state.messagesSnapshotUnsubscribe = null;
        state.commentsListeners.unsubscribe = null;
      })
      .addCase(stopAllRealtimeListeners.rejected, (state, action) => {
        console.error("Failed to stop real-time listeners:", action.payload);
      })

      .addCase(markCommentsAsReadRealtime.fulfilled, (state, action) => {
        console.log(`[REALTIME] Marked ${action.payload.commentIds.length} comments as read for user ${action.payload.user.name}`);
        // Real-time listener will update the state automatically
      })
      .addCase(markCommentsAsReadRealtime.rejected, (state, action) => {
        console.error("Failed to mark comments as read in real-time:", action.payload);
      })

      .addCase(getRealtimeListenerStatus.fulfilled, (state, action) => {
        console.log('[REALTIME] Listener status:', action.payload);
      });
  }
});

// Export actions
export const {
  setChats,
  setSelectedChat,
  setMessages,
  setNestedMessages,
  setLatestMessages,
  setSelectedPage,
  setActiveFilter,
  setMessengerChats,
  setInstagramChats,
  setWhatsappChats,
  setWhatsappChatMessages,
  setWhatsAppAccounts,
  setSelectedWhatsAccount,
  setSelectedWhatsappChat,
  setWhatsAppAccountDetails,
  setSelectedPhone,
  setBusinessPhoneNumbers,
  setStartDate,
  setEndDate,
  setFilteredData,
  setConversations,
  setPaginationMeta,
  setHasMore,
  setDisabledChat,
  setDisabledChatLimit,
  setPageImg,
  setLoadingPagination,
  setLoadingChats,
  setMessagesSnapshotUnsubscribe,
  setLatestMessagesUnsubscribe,
  setPollingInterval,
  selectChat,
  selectWhatsappChat,
  handleSelectWhatsAppAccount,
  resetFilteredChatsPages,
  debugMessageFlow,
  updateMessages,
  updateWhatsappChatMessages,
  updateWhatsappChatsWithFirebase,
  updateMessengerChats,
  updateInstagramChats,
  clearErrors,
  updateChats,
  updateLatestMessages,
  updateSelectedWhatsappChat,
  // Comments system actions
  setComments,
  addCommentToState,
  updateComment,
  removeComment,
  setCommentsModal,
  openCommentsModal,
  closeCommentsModal,
  setCommentsUnreadCount,
  incrementCommentsUnreadCount,
  resetCommentsUnreadCount,
  setCommentsLoading,
  setCommentsInitialLoading,
  setCommentsListener,
  clearCommentsListener,
  updateCommentsRealtime,
  markCommentsAsRead,
  resetNotificationSound,
  resetRefreshCommentsFlag,
  updateUnreadCommentCounts,
  updateUnreadMessageCounts,
  markMessagesAsRead,
} = metaBusinessSuiteSlice.actions;

// Export selectors
export const selectmetaBusinessSuite = (state) => state.metaBusinessSuite;
export const selectChats = (state) => state.metaBusinessSuite.chats;
export const selectWhatsappChats = (state) => state.metaBusinessSuite.whatsappChats;
export const selectSelectedChat = (state) => state.metaBusinessSuite.selectedChat;
export const selectSelectedWhatsappChat = (state) => state.metaBusinessSuite.selectedWhatsappChat;
export const selectMessages = createSelector(
  [state => state.metaBusinessSuite.nestedMessages, state => state.metaBusinessSuite.messages],
  (nestedMessages, messages) => [
    ...(nestedMessages || []),
    ...(messages || [])
  ]
);
export const selectLatestMessages = (state) => state.metaBusinessSuite.latestMessages;
export const selectWhatsappChatMessages = (state) => state.metaBusinessSuite.whatsappChatMessages;
export const selectSelectedPage = (state) => state.metaBusinessSuite.selectedPage;
export const selectActiveFilter = (state) => state.metaBusinessSuite.activeFilter;
export const selectLoadingStates = (state) => state.metaBusinessSuite.loading;
export const selectErrorStates = (state) => state.metaBusinessSuite.error;
export const selectDisabledChat = (state) => state.metaBusinessSuite.disabledChat;
export const selectDisabledChatLimit = (state) => state.metaBusinessSuite.disabledChatLimit;
export const selectPaginationMeta = (state) => state.metaBusinessSuite.paginationMeta;
export const selectLoadingPagination = (state) => state.metaBusinessSuite.loadingPagination;
export const selectInstagramChats = (state) => state.metaBusinessSuite.instagramChats;
export const selectMessengerChats = (state) => state.metaBusinessSuite.messengerChats;
export const selectLoadingChats = (state) => state.metaBusinessSuite.loadingChats;
export const selectHasMore = (state) => state.metaBusinessSuite.hasMore;
export const selectSelectedWhatsAccount = (state) => state.metaBusinessSuite.selectedWhatsAccount;
export const selectSelectedPhone = (state) => state.metaBusinessSuite.selectedPhone;
export const selectWhatsAppAccounts = (state) => state.metaBusinessSuite.whatsAppAccounts;
export const selectWhatsappLoading = (state) => state.metaBusinessSuite.loading;

// Comments system selectors
export const selectComments = (state) => state.metaBusinessSuite.comments;
export const selectCommentsModal = (state) => state.metaBusinessSuite.commentsModal;
export const selectCommentsUnreadCount = (state) => state.metaBusinessSuite.commentsModal.unreadCount;
export const selectCommentsLoading = (state) => state.metaBusinessSuite.commentsModal.loading;
export const selectCommentsInitialLoading = (state) => state.metaBusinessSuite.commentsModal.initialLoading;
export const selectCommentsModalOpen = (state) => state.metaBusinessSuite.commentsModal.isOpen;
export const selectCommentsListener = (state) => state.metaBusinessSuite.commentsListeners.unsubscribe;
export const selectChatUnreadComments = (state) => state.metaBusinessSuite.chatUnreadComments;
export const selectChatUnreadMessages = (state) => state.metaBusinessSuite.chatUnreadMessages;
export const selectShouldPlayNotificationSound = (state) => state.metaBusinessSuite.shouldPlayNotificationSound;
export const selectShouldRefreshComments = (state) => state.metaBusinessSuite.shouldRefreshComments;

// Memoized selector for comments with read status for current user
export const selectCommentsWithReadStatus = createSelector(
  [selectComments, (state, userId) => userId],
  (comments, userId) => {
    return comments.map(comment => ({
      ...comment,
      isReadByCurrentUser: comment.readBy?.some(receipt => receipt.userId === userId) || false,
      readByCount: comment.readBy?.length || 0
    }));
  }
);

// Memoized selector for unread comments count for current user
export const selectUnreadCommentsCount = createSelector(
  [selectComments, (state, userId) => userId],
  (comments, userId) => {
    return comments.filter(comment =>
      !comment.readBy?.some(receipt => receipt.userId === userId)
    ).length;
  }
);

// Selector for unread count of a specific chat
export const selectChatUnreadCount = createSelector(
  [selectChatUnreadComments, (state, chatId) => chatId],
  (chatUnreadComments, chatId) => {
    return chatUnreadComments[chatId]?.count || 0;
  }
);

// Selector for unread message count of a specific chat
export const selectChatUnreadMessageCount = createSelector(
  [selectChatUnreadMessages, (state, chatId) => chatId],
  (chatUnreadMessages, chatId) => {
    return chatUnreadMessages[chatId]?.count || 0;
  }
);

// Memoized selector that merges latest messages with chats (same as Context API)
export const selectMergedAndSortedChats = createSelector(
  [
    state => state.metaBusinessSuite.activeFilter,
    state => state.metaBusinessSuite.messengerChats,
    state => state.metaBusinessSuite.instagramChats,
    selectLatestMessages
  ],
  (activeFilter, messengerChats, instagramChats, latestMessages) => {
    if (!activeFilter) {
      return [];
    }

    let baseChats = [];
    if (activeFilter === 'messenger') {
      baseChats = messengerChats || [];
    } else if (activeFilter === 'instagram') {
      baseChats = instagramChats || [];
    } else {
      return [];
    }

    if (!Array.isArray(baseChats) || baseChats.length === 0) {
      return baseChats;
    }

    // Use the same logic as the Context API
    const merged = baseChats.map(chat => {
      if (!chat || !chat.participants || !chat.participants.data) {
        return chat;
      }

      // Get the participant ID (same logic as Context)
      const participantIndex = activeFilter === 'instagram' ? 1 : 0;
      const participantId = chat.participants.data[participantIndex]?.id;

      if (!participantId) {
        return chat;
      }

      // Use the same extractLatestMessage function as Context
      const latestMsg = extractLatestMessage(participantId, latestMessages);

      if (latestMsg) {
        return {
          ...chat,
          message: latestMsg.message,
          updated_time: latestMsg.created_time,
          created_time: latestMsg.created_time,
          type: latestMsg.type || chat.type,
          url: latestMsg.url || chat.url,
        };
      }
      return chat;
    });

    // Sort by updated_time or created_time
    const sorted = merged.sort((a, b) => new Date(b.updated_time || b.created_time) - new Date(a.updated_time || a.created_time));
    return sorted;
  }
);

// Memoized selector for WhatsApp chats that merges with latest messages
export const selectMergedAndSortedWhatsappChats = createSelector(
  [
    state => state.metaBusinessSuite.whatsappChats,
    selectLatestMessages
  ],
  (whatsappChats, latestMessages) => {
    if (!Array.isArray(whatsappChats) || whatsappChats.length === 0) {
      return whatsappChats;
    }

    // Merge WhatsApp chats with latest messages
    const merged = whatsappChats.map(chat => {
      if (!chat) {
        return chat;
      }

      // For WhatsApp, we need to find the latest message for this chat
      // The chat has sender_phone_number, and messages have 'sender' or 'from' field
      const chatPhoneNumber = chat.sender_phone_number;

      if (!chatPhoneNumber) {
        return chat;
      }

      // Find all messages for this WhatsApp chat
      const chatMessages = latestMessages?.filter(msg => {
        // Check if the message is from or to this chat
        // Handle both 'sender' and 'from' fields for compatibility
        const msgSender = msg.sender || msg.from;
        const msgRecipient = msg.recipient;

        const isMatch = msgSender === chatPhoneNumber || msgRecipient === chatPhoneNumber;
        return isMatch;
      });

      if (chatMessages && chatMessages.length > 0) {
        // Find the latest message
        const latestMsg = chatMessages.sort((a, b) => {
          const dateA = new Date(a.created_time || a.created_at);
          const dateB = new Date(b.created_time || b.created_at);
          return dateB - dateA; // Sort in descending order
        })[0];

        return {
          ...chat,
          last_message: {
            message: latestMsg.message,
            created_at: latestMsg.created_time || latestMsg.created_at,
            updated_at: latestMsg.created_time || latestMsg.created_at,
            from: latestMsg.sender || latestMsg.from,
            type: latestMsg.type || "text",
            message_id: latestMsg.message_id,
            timestamp: Math.floor(new Date(latestMsg.created_time || latestMsg.created_at).getTime() / 1000).toString(),
            url: latestMsg.url || null
          },
          updated_at: latestMsg.created_time || latestMsg.created_at,
        };
      }
      return chat;
    });

    // Sort by updated_at or created_at
    const sorted = merged.sort((a, b) => {
      const dateA = a.last_message?.created_at || a.updated_at || a.created_at;
      const dateB = b.last_message?.created_at || b.updated_at || b.created_at;
      return new Date(dateB) - new Date(dateA);
    });

    return sorted;
  }
);

// Helper function to format date (moved from context)
export const formatDate = (timestamp) => {
  let messageDate;
  if (timestamp?.seconds && timestamp?.nanoseconds) {
    const milliseconds =
      timestamp.seconds * 1000 + timestamp.nanoseconds / 1e6;
    messageDate = new Date(milliseconds);
  } else {
    try {
      messageDate = new Date(timestamp);
    } catch (error) {
      console.error("Error parsing timestamp:", error);
    }
  }

  const currentDate = new Date();
  return formatTimestamp(messageDate, currentDate);
};

export default metaBusinessSuiteSlice.reducer;
