/**
 * Custom hook for managing real-time chat functionality
 *
 * This hook provides a simple interface for setting up and managing
 * real-time listeners for chats, messages, and comments with automatic
 * cleanup and enhanced read receipt functionality.
 */

import { useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  startRealtimeChatsListener,
  startRealtimeMessagesListener,
  startRealtimeCommentsListener,
  stopAllRealtimeListeners,
  markCommentsAsReadRealtime,
  getRealtimeListenerStatus
} from '../redux/features/metaBusinessChatSlice';

/**
 * Hook for managing real-time chat functionality
 * @param {Object} options - Configuration options
 * @returns {Object} - Real-time chat utilities
 */
export const useRealtimeChat = (options = {}) => {
  const dispatch = useDispatch();
  const activeListenersRef = useRef(new Set());

  // Redux selectors
  const selectedPage = useSelector(state => state.metaBusinessSuite?.selectedPage);
  const selectedChat = useSelector(state => state.metaBusinessSuite?.selectedChat);
  const selectedWhatsappChat = useSelector(state => state.metaBusinessSuite?.selectedWhatsappChat);
  const currentUser = useSelector(state => state.auth?.user);
  const comments = useSelector(state => state.metaBusinessSuite?.comments);
  const commentsModal = useSelector(state => state.metaBusinessSuite?.commentsModal);

  // Default options
  const {
    autoStartChatsListener = true,
    autoStartMessagesListener = true,
    autoStartCommentsListener = false, // Only start when comments modal is open
    autoMarkCommentsAsRead = true,
    cleanupOnUnmount = true
  } = options;

  /**
   * Start listening to all chats for the current page
   */
  const startChatsListener = useCallback(async () => {
    if (!selectedPage?.id) {
      console.warn('[REALTIME] Cannot start chats listener: no page selected');
      return;
    }

    try {
      await dispatch(startRealtimeChatsListener({
        pageId: selectedPage.id
      })).unwrap();

      activeListenersRef.current.add(`chats_${selectedPage.id}`);
      console.log(`[REALTIME] Started chats listener for page ${selectedPage.id}`);
    } catch (error) {
      console.error('[REALTIME] Failed to start chats listener:', error);
    }
  }, [dispatch, selectedPage?.id]);

  /**
   * Start listening to messages for the current chat
   */
  const startMessagesListener = useCallback(async () => {
    const currentChat = selectedChat || selectedWhatsappChat;
    if (!currentChat || !selectedPage?.id) {
      console.warn('[REALTIME] Cannot start messages listener: no chat or page selected');
      return;
    }

    // Determine chat type and ID
    const chatType = selectedWhatsappChat ? 'whatsapp' :
      selectedChat?.flage === 'instagram' ? 'instagram' : 'messenger';

    const chatId = chatType === 'whatsapp'
      ? currentChat.sender_phone_number?.toString().trim().replace(/^\+|\s+/g, "")
      : currentChat.id?.toString();

    if (!chatId) {
      console.warn('[REALTIME] Cannot start messages listener: invalid chat ID');
      return;
    }

    try {
      await dispatch(startRealtimeMessagesListener({
        chatId,
        chatType,
        pageId: selectedPage.id
      })).unwrap();

      activeListenersRef.current.add(`messages_${selectedPage.id}_${chatId}`);
      console.log(`[REALTIME] Started messages listener for chat ${chatId}`);
    } catch (error) {
      console.error('[REALTIME] Failed to start messages listener:', error);
    }
  }, [dispatch, selectedChat, selectedWhatsappChat, selectedPage?.id]);

  /**
   * Start listening to comments for the current chat
   */
  const startCommentsListener = useCallback(async () => {
    const currentChat = selectedChat || selectedWhatsappChat;
    if (!currentChat || !selectedPage?.id || !currentUser) {
      console.warn('[REALTIME] Cannot start comments listener: missing requirements');
      return;
    }

    // Determine chat type and ID
    const chatType = selectedWhatsappChat ? 'whatsapp' :
      selectedChat?.flage === 'instagram' ? 'instagram' : 'messenger';

    const chatId = chatType === 'whatsapp'
      ? currentChat.sender_phone_number?.toString().trim().replace(/^\+|\s+/g, "")
      : currentChat.id?.toString();

    if (!chatId) {
      console.warn('[REALTIME] Cannot start comments listener: invalid chat ID');
      return;
    }

    try {
      await dispatch(startRealtimeCommentsListener({
        chatId,
        chatType,
        pageId: selectedPage.id,
        autoMarkAsRead: autoMarkCommentsAsRead,
        autoScrollToBottom: true
      })).unwrap();

      activeListenersRef.current.add(`comments_${selectedPage.id}_${chatId}`);
      console.log(`[REALTIME] Started comments listener for chat ${chatId}`);
    } catch (error) {
      console.error('[REALTIME] Failed to start comments listener:', error);
    }
  }, [dispatch, selectedChat, selectedWhatsappChat, selectedPage?.id, currentUser, autoMarkCommentsAsRead]);

  /**
   * Mark specific comments as read
   */
  const markCommentsAsRead = useCallback(async (commentIds) => {
    const currentChat = selectedChat || selectedWhatsappChat;
    if (!currentChat || !selectedPage?.id || !currentUser || !commentIds?.length) {
      console.warn('[REALTIME] Cannot mark comments as read: missing requirements');
      return;
    }

    const chatType = selectedWhatsappChat ? 'whatsapp' :
      selectedChat?.flage === 'instagram' ? 'instagram' : 'messenger';

    const chatId = chatType === 'whatsapp'
      ? currentChat.sender_phone_number?.toString().trim().replace(/^\+|\s+/g, "")
      : currentChat.id?.toString();

    if (!chatId) {
      console.warn('[REALTIME] Cannot mark comments as read: invalid chat ID');
      return;
    }

    try {
      await dispatch(markCommentsAsReadRealtime({
        chatId,
        chatType,
        pageId: selectedPage.id,
        commentIds
      })).unwrap();

      console.log(`[REALTIME] Marked ${commentIds.length} comments as read`);
    } catch (error) {
      console.error('[REALTIME] Failed to mark comments as read:', error);
    }
  }, [dispatch, selectedChat, selectedWhatsappChat, selectedPage?.id, currentUser]);

  /**
   * Stop all real-time listeners
   */
  const stopAllListeners = useCallback(async () => {
    try {
      await dispatch(stopAllRealtimeListeners()).unwrap();
      activeListenersRef.current.clear();
      console.log('[REALTIME] Stopped all listeners');
    } catch (error) {
      console.error('[REALTIME] Failed to stop listeners:', error);
    }
  }, [dispatch]);

  /**
   * Get current listener status
   */
  const getListenerStatus = useCallback(async () => {
    try {
      const status = await dispatch(getRealtimeListenerStatus()).unwrap();
      return status;
    } catch (error) {
      console.error('[REALTIME] Failed to get listener status:', error);
      return null;
    }
  }, [dispatch]);

  // Auto-start listeners based on options and state changes
  useEffect(() => {
    if (autoStartChatsListener && selectedPage?.id) {
      startChatsListener();
    }
  }, [autoStartChatsListener, selectedPage?.id, startChatsListener]);

  useEffect(() => {
    if (autoStartMessagesListener && (selectedChat || selectedWhatsappChat)) {
      startMessagesListener();
    }
  }, [autoStartMessagesListener, selectedChat, selectedWhatsappChat, startMessagesListener]);

  useEffect(() => {
    if (autoStartCommentsListener && commentsModal?.isOpen) {
      startCommentsListener();
    }
  }, [autoStartCommentsListener, commentsModal?.isOpen, startCommentsListener]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (cleanupOnUnmount) {
        stopAllListeners();
      }
    };
  }, [cleanupOnUnmount, stopAllListeners]);

  return {
    // Listener controls
    startChatsListener,
    startMessagesListener,
    startCommentsListener,
    stopAllListeners,

    // Comment utilities
    markCommentsAsRead,

    // Status
    getListenerStatus,
    activeListeners: Array.from(activeListenersRef.current),

    // State
    isChatsListenerActive: activeListenersRef.current.has(`chats_${selectedPage?.id}`),
    isMessagesListenerActive: selectedChat || selectedWhatsappChat ?
      activeListenersRef.current.has(`messages_${selectedPage?.id}_${(selectedWhatsappChat ?
          selectedWhatsappChat.sender_phone_number?.toString().trim().replace(/^\+|\s+/g, "") :
          selectedChat?.id?.toString()) || 'unknown'
        }`) : false,
    isCommentsListenerActive: selectedChat || selectedWhatsappChat ?
      activeListenersRef.current.has(`comments_${selectedPage?.id}_${(selectedWhatsappChat ?
          selectedWhatsappChat.sender_phone_number?.toString().trim().replace(/^\+|\s+/g, "") :
          selectedChat?.id?.toString()) || 'unknown'
        }`) : false,

    // Data
    comments,
    commentsModal,
    currentUser
  };
};

export default useRealtimeChat;
