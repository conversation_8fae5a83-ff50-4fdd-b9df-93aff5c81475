/**
 * Real-time Firebase Listener Manager
 *
 * This service manages all real-time Firebase listeners for chats and comments,
 * providing centralized listener management, automatic cleanup, and enhanced
 * read receipt functionality.
 */

import {
    collection,
    query,
    orderBy,
    onSnapshot,
    where,
    doc,
    updateDoc,
    arrayUnion,
    serverTimestamp,
    getDoc,
    collectionGroup,
    startAfter,
    limit
} from 'firebase/firestore';
import { db } from '../../utils/firebase.config';
import { getCollectionPaths, getChatIdentifier, determineChatType } from './collectionPaths';
import store from '../../redux/store';

/**
 * Real-time listener manager class
 */
class RealtimeManager {
    constructor() {
        this.listeners = new Map(); // Store active listeners
        this.chatListeners = new Map(); // Store chat-specific listeners
        this.commentListeners = new Map(); // Store comment-specific listeners
        this.readReceiptQueue = new Map(); // Queue for batching read receipts
        this.readReceiptTimer = null;
    }

    /**
     * Start listening to all chats for a specific page
     * @param {string} pageId - The page identifier
     * @param {Function} onChatsUpdate - Callback for chat updates
     * @param {Object} options - Listener options
     * @returns {Function} - Unsubscribe function
     */
    listenToAllChats(pageId, onChatsUpdate, options = {}) {
        const listenerId = `all_chats_${pageId}`;

        // Clean up existing listener
        this.cleanup(listenerId);

        try {
            // Query all chats for the page using the combined ID structure
            const chatsQuery = query(
                collection(db, 'chats'),
                where('pageId', '==', pageId),
                orderBy('lastActivity', 'desc')
            );

            const unsubscribe = onSnapshot(chatsQuery, (snapshot) => {
                const chats = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data(),
                    _lastUpdated: new Date().toISOString()
                }));

                console.log(`[REALTIME] Received ${chats.length} chat updates for page ${pageId}`);
                onChatsUpdate(chats);
            }, (error) => {
                console.error(`[REALTIME] Error in chats listener for page ${pageId}:`, error);
                onChatsUpdate([], error);
            });

            this.listeners.set(listenerId, unsubscribe);
            return () => this.cleanup(listenerId);

        } catch (error) {
            console.error(`[REALTIME] Failed to set up chats listener for page ${pageId}:`, error);
            onChatsUpdate([], error);
            return () => { };
        }
    }

    /**
     * Start listening to messages for a specific chat
     * @param {string} chatId - The chat identifier
     * @param {string} chatType - The chat type
     * @param {string} pageId - The page identifier
     * @param {Function} onMessagesUpdate - Callback for message updates
     * @param {Object} options - Listener options
     * @returns {Function} - Unsubscribe function
     */
    listenToMessages(chatId, chatType, pageId, onMessagesUpdate, options = {}) {
        const listenerId = `messages_${pageId}_${chatId}`;

        // Clean up existing listener
        this.cleanup(listenerId);

        try {
            const paths = getCollectionPaths(chatId, chatType, pageId);
            if (!paths.messages) {
                throw new Error(`Unable to determine messages path for chat ${chatId}`);
            }

            const messagesQuery = query(
                collection(db, ...paths.messages.split('/')),
                orderBy('created_time', 'asc')
            );

            const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
                const messages = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data(),
                    _lastUpdated: new Date().toISOString()
                }));

                console.log(`[REALTIME] Received ${messages.length} message updates for chat ${chatId}`);
                onMessagesUpdate(messages);

                // Trigger unread count update for messages
                const event = new CustomEvent('messagesUpdated', {
                    detail: { messages, chatId, chatType, pageId }
                });
                window.dispatchEvent(event);
            }, (error) => {
                console.error(`[REALTIME] Error in messages listener for chat ${chatId}:`, error);
                onMessagesUpdate([], error);
            });

            this.listeners.set(listenerId, unsubscribe);
            this.chatListeners.set(chatId, listenerId);
            return () => this.cleanup(listenerId);

        } catch (error) {
            console.error(`[REALTIME] Failed to set up messages listener for chat ${chatId}:`, error);
            onMessagesUpdate([], error);
            return () => { };
        }
    }

    /**
     * Start listening to comments for a specific chat with enhanced read receipt tracking
     * @param {string} chatId - The chat identifier
     * @param {string} chatType - The chat type
     * @param {string} pageId - The page identifier
     * @param {Function} onCommentsUpdate - Callback for comment updates
     * @param {Object} currentUser - Current user information
     * @param {Object} options - Listener options
     * @returns {Function} - Unsubscribe function
     */
    listenToComments(chatId, chatType, pageId, onCommentsUpdate, currentUser, options = {}) {
        const listenerId = `comments_${pageId}_${chatId}`;

        // Clean up existing listener
        this.cleanup(listenerId);

        try {
            const paths = getCollectionPaths(chatId, chatType, pageId);
            if (!paths.comments) {
                throw new Error(`Unable to determine comments path for chat ${chatId}`);
            }

            const commentsQuery = query(
                collection(db, ...paths.comments.split('/')),
                orderBy('createdAt', 'asc')
            );

            const unsubscribe = onSnapshot(commentsQuery, (snapshot) => {
                const comments = snapshot.docs.map(doc => {
                    const data = doc.data();
                    return {
                        id: doc.id,
                        ...data,
                        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
                        readBy: data.readBy || [],
                        _lastUpdated: new Date().toISOString()
                    };
                });

                console.log(`[REALTIME] Received ${comments.length} comment updates for chat ${chatId}`);

                // Auto-mark new comments as read if user is actively viewing
                if (currentUser && options.autoMarkAsRead) {
                    this.queueReadReceipts(chatId, chatType, pageId, comments, currentUser);
                }

                onCommentsUpdate(comments);

                // Trigger scroll to bottom if specified in options
                if (options.autoScrollToBottom && comments.length > 0) {
                    // Small delay to ensure DOM is updated
                    setTimeout(() => {
                        const event = new CustomEvent('commentsUpdated', {
                            detail: { comments, chatId, scrollToBottom: true }
                        });
                        window.dispatchEvent(event);
                    }, 100);
                }

                // Additional check: Don't trigger notifications if comments modal is open
                // This provides an extra layer of protection against notification spam
                const isCommentsModalOpen = document.querySelector('.comments-modal') !== null;
                if (isCommentsModalOpen) {
                    console.log('[REALTIME] Comments modal is open - suppressing notifications');
                }
            }, (error) => {
                console.error(`[REALTIME] Error in comments listener for chat ${chatId}:`, error);
                onCommentsUpdate([], error);
            });

            this.listeners.set(listenerId, unsubscribe);
            this.commentListeners.set(chatId, listenerId);
            return () => this.cleanup(listenerId);

        } catch (error) {
            console.error(`[REALTIME] Failed to set up comments listener for chat ${chatId}:`, error);
            onCommentsUpdate([], error);
            return () => { };
        }
    }

    /**
     * Queue read receipts for batch processing
     * @param {string} chatId - The chat identifier
     * @param {string} chatType - The chat type
     * @param {string} pageId - The page identifier
     * @param {Array} comments - Array of comments
     * @param {Object} currentUser - Current user information
     */
    queueReadReceipts(chatId, chatType, pageId, comments, currentUser) {
        const unreadComments = comments.filter(comment => {
            // Check if current user has already read this comment
            return !comment.readBy?.some(receipt => receipt.userId === currentUser.id);
        });

        if (unreadComments.length === 0) return;

        const queueKey = `${pageId}_${chatId}`;
        this.readReceiptQueue.set(queueKey, {
            chatId,
            chatType,
            pageId,
            commentIds: unreadComments.map(c => c.id),
            user: currentUser,
            timestamp: Date.now()
        });

        // Debounce read receipt updates
        if (this.readReceiptTimer) {
            clearTimeout(this.readReceiptTimer);
        }

        this.readReceiptTimer = setTimeout(() => {
            this.processReadReceiptQueue();
        }, 1000); // Wait 1 second before processing
    }

    /**
     * Process queued read receipts
     */
    async processReadReceiptQueue() {
        const queue = Array.from(this.readReceiptQueue.values());
        this.readReceiptQueue.clear();

        for (const item of queue) {
            try {
                await this.markCommentsAsReadInFirebase(
                    item.chatId,
                    item.chatType,
                    item.pageId,
                    item.commentIds,
                    item.user
                );
            } catch (error) {
                console.error('[REALTIME] Error processing read receipt:', error);
            }
        }
    }

    /**
     * Listen to all messages across all chats for a specific page
     * This approach monitors known chats and sets up individual listeners
     * @param {string} pageId - The page identifier
     * @param {Function} onNewMessage - Callback for new messages (message, senderId, chatType)
     * @param {Function} onLatestMessagesUpdate - Callback for latest messages updates
     * @param {Object} options - Listener options
     * @returns {Function} - Unsubscribe function
     */
    listenToAllPageMessages(pageId, onNewMessage, onLatestMessagesUpdate, options = {}) {
        const listenerId = `page_messages_${pageId}`;

        // Clean up existing listener
        this.cleanup(listenerId);

        try {
            console.log(`[REALTIME] Setting up page-level message listener for page ${pageId}`);

            // Get current chats from Redux to know which senders to monitor
            const state = store.getState().metaBusinessSuite;
            const allChats = state?.chats || [];
            const allLatestMessages = state?.latestMessages || [];

            // Debug: Check what fields the chats actually have
            if (allChats.length > 0) {
                console.log(`[REALTIME] Sample chat object keys:`, Object.keys(allChats[0]));
                console.log(`[REALTIME] Sample chat object:`, allChats[0]);
            }
            if (allLatestMessages.length > 0) {
                console.log(`[REALTIME] Sample latest message keys:`, Object.keys(allLatestMessages[0]));
                console.log(`[REALTIME] Sample latest message:`, allLatestMessages[0]);
            }

            // For now, don't filter by page - use all chats
            // TODO: Fix this once we know the correct field names
            const chats = allChats;
            const latestMessages = allLatestMessages;

            console.log(`[REALTIME] Using all chats for page ${pageId}: ${chats.length} chats`);
            console.log(`[REALTIME] Using all latest messages for page ${pageId}: ${latestMessages.length} messages`);
            console.warn(`[REALTIME] WARNING: Not filtering by page - this will create listeners for all chats!`);

            // Combine chats and latest messages to get all known senders FOR THIS PAGE ONLY
            const knownSenders = new Set();

            // Add senders from current chats - try multiple approaches
            chats.forEach(chat => {
                // Method 1: Try participant data
                if (chat.participants?.data) {
                    const chatType = chat.flage === 'instagram' ? 'instagram' : 'messenger';
                    const participantIndex = chatType === 'instagram' ? 1 : 0;
                    const senderId = chat.participants.data[participantIndex]?.id;
                    if (senderId) {
                        knownSenders.add(senderId);
                    }
                }

                // Method 2: Try direct chat ID (fallback)
                if (chat.id) {
                    knownSenders.add(chat.id);
                }

                // Method 3: Try sender field
                if (chat.sender) {
                    knownSenders.add(chat.sender);
                }
            });

            // Add senders from latest messages - try multiple fields
            latestMessages.forEach(msg => {
                if (msg.sender) knownSenders.add(msg.sender);
                if (msg.senderId) knownSenders.add(msg.senderId);
                if (msg.id) knownSenders.add(msg.id);

                // Try to extract from participants if available
                if (msg.participants?.data) {
                    const chatType = msg.flage === 'instagram' ? 'instagram' : 'messenger';
                    const participantIndex = chatType === 'instagram' ? 1 : 0;
                    const senderId = msg.participants.data[participantIndex]?.id;
                    if (senderId) {
                        knownSenders.add(senderId);
                    }
                }
            });

            console.log(`[REALTIME] Found ${knownSenders.size} known senders for page ${pageId}:`, Array.from(knownSenders));
            console.log(`[REALTIME] Chats available:`, chats.length);
            console.log(`[REALTIME] Latest messages available:`, latestMessages.length);

            // If no known senders, don't set up any listeners
            if (knownSenders.size === 0) {
                console.warn(`[REALTIME] No known senders found for page ${pageId}. No listeners will be set up.`);
                console.warn(`[REALTIME] This usually means chats don't have page_id field or the filtering logic needs adjustment.`);

                // Return a dummy unsubscribe function
                const dummyUnsubscribe = () => {
                    console.log(`[REALTIME] Dummy unsubscribe called for page ${pageId}`);
                };
                this.listeners.set(listenerId, dummyUnsubscribe);
                return dummyUnsubscribe;
            }

            // Set up listeners for each known sender
            const unsubscribeFunctions = [];

            knownSenders.forEach(senderId => {
                const senderListenerId = `sender_messages_${pageId}_${senderId}`;

                try {
                    const messagesQuery = query(
                        collection(db, pageId, senderId, 'messages'),
                        orderBy('created_time', 'desc'),
                        limit(50)
                    );

                    const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
                        if (snapshot.empty) return;

                        const messages = snapshot.docs.map(doc => ({
                            id: doc.id,
                            ...doc.data(),
                            _lastUpdated: new Date().toISOString(),
                            _senderId: senderId,
                            _pageId: pageId
                        }));

                        console.log(`[REALTIME] Received ${messages.length} messages for sender ${senderId} in page ${pageId}`);

                        // Determine chat type
                        const chatType = this.determineChatTypeFromSenderId(senderId);

                        // Call callbacks
                        messages.forEach(message => {
                            onNewMessage(message, senderId, chatType, { senderId });
                        });

                        // Update latest messages
                        if (messages.length > 0) {
                            const latestMessage = messages[0];
                            onLatestMessagesUpdate({
                                senderId,
                                pageId,
                                chatType,
                                latestMessage,
                                senderData: { senderId },
                                totalMessages: messages.length
                            });
                        }

                    }, (error) => {
                        console.error(`[REALTIME] Error in sender messages listener for ${senderId}:`, error);
                    });

                    this.listeners.set(senderListenerId, unsubscribe);
                    unsubscribeFunctions.push(() => this.cleanup(senderListenerId));

                } catch (error) {
                    console.error(`[REALTIME] Failed to set up listener for sender ${senderId}:`, error);
                }
            });

            // Return a function that cleans up all listeners
            const masterUnsubscribe = () => {
                unsubscribeFunctions.forEach(unsub => unsub());
                this.cleanup(listenerId);
            };

            this.listeners.set(listenerId, masterUnsubscribe);
            return masterUnsubscribe;

        } catch (error) {
            console.error(`[REALTIME] Failed to set up page messages listener for page ${pageId}:`, error);
            return () => { };
        }
    }



    /**
     * Determine chat type from sender ID pattern
     * @param {string} senderId - The sender identifier
     * @returns {string} - The chat type
     */
    determineChatTypeFromSenderId(senderId) {
        // Add logic to determine chat type from sender ID pattern
        // This is a simple fallback - you might want to enhance this
        if (senderId && senderId.includes('whatsapp')) {
            return 'whatsapp';
        } else if (senderId && senderId.includes('instagram')) {
            return 'instagram';
        }
        return 'messenger'; // Default fallback
    }

    /**
     * Set up message listener for a specific sender under a page
     * @param {string} pageId - The page identifier
     * @param {string} senderId - The sender identifier
     * @param {Object} senderData - The sender document data
     * @param {Function} onNewMessage - Callback for new messages
     * @param {Function} onLatestMessagesUpdate - Callback for latest messages updates
     */
    setupSenderMessageListener(pageId, senderId, senderData, onNewMessage, onLatestMessagesUpdate) {
        const senderListenerId = `sender_messages_${pageId}_${senderId}`;

        // Clean up existing listener for this sender
        this.cleanup(senderListenerId);

        try {
            // Listen to messages for this specific sender
            const messagesQuery = query(
                collection(db, pageId, senderId, 'messages'),
                orderBy('created_time', 'desc'),
                limit(50) // Limit to recent messages for performance
            );

            const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
                if (snapshot.empty) return;

                const messages = snapshot.docs.map(doc => ({
                    id: doc.id,
                    ...doc.data(),
                    _lastUpdated: new Date().toISOString(),
                    _senderId: senderId,
                    _pageId: pageId
                }));

                console.log(`[REALTIME] Received ${messages.length} messages for sender ${senderId} in page ${pageId}`);

                // Determine chat type from sender data or message data
                const chatType = this.determineChatTypeFromData(senderData, messages[0]);

                // Call the new message callback for each message
                messages.forEach(message => {
                    onNewMessage(message, senderId, chatType, senderData);
                });

                // Update latest messages with the most recent message
                if (messages.length > 0) {
                    const latestMessage = messages[0]; // Already sorted by created_time desc
                    onLatestMessagesUpdate({
                        senderId,
                        pageId,
                        chatType,
                        latestMessage,
                        senderData,
                        totalMessages: messages.length
                    });
                }

            }, (error) => {
                console.error(`[REALTIME] Error in sender messages listener for ${senderId}:`, error);
            });

            this.listeners.set(senderListenerId, unsubscribe);

        } catch (error) {
            console.error(`[REALTIME] Failed to set up sender messages listener for ${senderId}:`, error);
        }
    }

    /**
     * Determine chat type from available data
     * @param {Object} senderData - The sender document data
     * @param {Object} messageData - The message data
     * @returns {string} - The chat type (messenger, instagram, whatsapp)
     */
    determineChatTypeFromData(senderData, messageData) {
        // Try to determine from sender data first
        if (senderData?.chat_type) return senderData.chat_type;
        if (senderData?.type) return senderData.type;

        // Try to determine from message data
        if (messageData?.chat_type) return messageData.chat_type;
        if (messageData?.type) return messageData.type;

        // Try to determine from sender ID pattern or other indicators
        if (senderData?.sender_phone_number || messageData?.sender_phone_number) {
            return 'whatsapp';
        }

        // Default fallback - could be enhanced with more logic
        return 'messenger';
    }

    /**
     * Mark comments as read in Firebase with real-time updates
     * @param {string} chatId - The chat identifier
     * @param {string} chatType - The chat type
     * @param {string} pageId - The page identifier
     * @param {Array} commentIds - Array of comment IDs
     * @param {Object} user - User information
     */
    async markCommentsAsReadInFirebase(chatId, chatType, pageId, commentIds, user) {
        try {
            const paths = getCollectionPaths(chatId, chatType, pageId);
            if (!paths.comments) {
                throw new Error(`Unable to determine comments path for chat ${chatId}`);
            }

            const readReceipt = {
                userId: user.id,
                userName: user.name,
                userPhoto: user.photo || null,
                readAt: new Date().toISOString()
            };

            // Update each comment with read receipt
            const updatePromises = commentIds.map(async (commentId) => {
                const commentRef = doc(db, ...paths.comments.split('/'), commentId);

                // Check if user already has a read receipt
                const commentDoc = await getDoc(commentRef);
                if (commentDoc.exists()) {
                    const currentData = commentDoc.data();
                    const existingReadBy = currentData.readBy || [];

                    const userAlreadyRead = existingReadBy.some(receipt =>
                        receipt.userId === user.id
                    );

                    if (!userAlreadyRead) {
                        await updateDoc(commentRef, {
                            readBy: arrayUnion(readReceipt),
                            lastReadUpdate: serverTimestamp()
                        });
                        console.log(`[REALTIME] Marked comment ${commentId} as read by ${user.name}`);
                    }
                }
            });

            await Promise.all(updatePromises);
        } catch (error) {
            console.error('[REALTIME] Error marking comments as read:', error);
            throw error;
        }
    }

    /**
     * Clean up a specific listener
     * @param {string} listenerId - The listener identifier
     */
    cleanup(listenerId) {
        const unsubscribe = this.listeners.get(listenerId);
        if (unsubscribe) {
            try {
                unsubscribe();
                console.log(`[REALTIME] Cleaned up listener: ${listenerId}`);
            } catch (error) {
                console.warn(`[REALTIME] Error cleaning up listener ${listenerId}:`, error);
            }
            this.listeners.delete(listenerId);
        }
    }

    /**
     * Clean up all listeners
     */
    cleanupAll() {
        console.log(`[REALTIME] Cleaning up ${this.listeners.size} listeners`);

        for (const [listenerId, unsubscribe] of this.listeners) {
            try {
                unsubscribe();
            } catch (error) {
                console.warn(`[REALTIME] Error cleaning up listener ${listenerId}:`, error);
            }
        }

        this.listeners.clear();
        this.chatListeners.clear();
        this.commentListeners.clear();
        this.readReceiptQueue.clear();

        if (this.readReceiptTimer) {
            clearTimeout(this.readReceiptTimer);
            this.readReceiptTimer = null;
        }
    }

    /**
     * Get active listener count
     * @returns {number} - Number of active listeners
     */
    getActiveListenerCount() {
        return this.listeners.size;
    }

    /**
     * Get listener status
     * @returns {Object} - Listener status information
     */
    getStatus() {
        return {
            totalListeners: this.listeners.size,
            chatListeners: this.chatListeners.size,
            commentListeners: this.commentListeners.size,
            queuedReadReceipts: this.readReceiptQueue.size,
            activeListeners: Array.from(this.listeners.keys())
        };
    }
}

// Create singleton instance
const realtimeManager = new RealtimeManager();

export default realtimeManager;

// Export individual methods for convenience
export const {
    listenToAllChats,
    listenToMessages,
    listenToComments,
    markCommentsAsReadInFirebase,
    cleanup,
    cleanupAll,
    getActiveListenerCount,
    getStatus
} = realtimeManager;
